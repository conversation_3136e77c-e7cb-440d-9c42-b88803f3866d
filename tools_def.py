"""
Tool definitions for Gemini AI function calling
Natural language focused - let the LLM decide which function to call based on user intent
"""

TOOL_DEF = [
    {
        "function_declarations": [
            {
                "name": "get_all_cars",
                "description": "Use this when customers want to see all available cars, browse the complete inventory, or ask 'show me cars', 'what cars do you have', 'all cars', 'i want to buy a car', 'looking for cars', 'car options', etc. Shows ONLY Maruti Suzuki cars from both Arena (affordable) and Nexa (premium) categories with interactive buttons. DO NOT use this for trucks or commercial vehicles.",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_cars_by_category",
                "description": "Use this when customers specifically ask for Arena cars (affordable, entry-level) or Nexa cars (premium, luxury). Examples: 'show me arena cars', 'nexa cars', 'premium cars', 'budget cars', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "category": {
                            "type": "string",
                            "description": "The dealership category: 'arena' for affordable cars or 'nexa' for premium cars",
                            "enum": ["arena", "nexa"]
                        }
                    },
                    "required": ["category"]
                }
            },
            {
                "name": "search_cars",
                "description": "Use this when customers search for specific cars by name, features, fuel type, price range, or category. Examples: 'swift', 'hatchback cars', 'CNG cars', 'cars under 10 lakhs', 'diesel cars', 'automatic cars', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The customer's search query - can be car name, fuel type, price range, category, or any car-related criteria"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "show_car_details",
                "description": "Use this when customers ask for detailed information about a specific car model. Examples: 'tell me about Swift', 'Baleno details', 'show me Grand Vitara specs', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "car_id": {
                            "type": "string",
                            "description": "The name or ID of the specific car the customer wants details about"
                        }
                    },
                    "required": ["car_id"]
                }
            }
        ]
    }
]
