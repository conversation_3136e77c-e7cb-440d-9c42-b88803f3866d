{"🔁Back to main menu": [{"message": "✨ Please select the services you're looking for! 🛠️💼📋💬💡 Let's get started! 🚀😊", "data": ["Maruti Suzuki", "Tata Commercials", "Book a Service", "Used Cars", "Explore More", "Purchase Accessories", "Request a Call Back", "Live Talk", "Book a Test Drive", "Contact Us"], "data_type": "list"}], "Main Menu": [{"message": "Hi 👋 Welcome to *Bhandari Automobiles*\n How can I assist you today?", "data": ["Maruti Suzuki", "Tata Commercials", "Book a Service"], "data_type": "button"}, {"message": "👇For more options click below 🔗✨", "data": ["Maruti Suzuki", "Book a Service", "Used Cars", "Explore More", "Purchase Accessories", "Request a Call Back", "Live Talk", "Book a Test Drive", "Contact Us"], "data_type": "list"}], "Maruti Suzuki": [{"message": "Great! Please select a brand:", "data": ["ARENA", "NEXA"], "data_type": "button"}], "ARENA": [{"message": "Here are the popular *Maruti Suzuki Arena* segment by Bhandari Automobiles.\n\nPlease choose the cars to continue?", "data": ["Alto K10", "Swift", "Brezza", "Wagon R", "Dzire", "<PERSON><PERSON><PERSON>", "S-Presso", "Ertiga", "Eeco", "🔁Back to main menu"], "data_type": "list"}], "NEXA": [{"message": "Here are the popular *NEXA Experience* by Bhandari Automobiles.\n\nWhich model would you like to Explore?", "data": ["<PERSON><PERSON>", "Baleno", "Ciaz", "Fronx", "Grand Vitara", "<PERSON><PERSON><PERSON>", "XL6", "🔁Back to main menu"], "data_type": "list"}], "Tata Commercials": [{"message": "Here are the popular *Tata Commercials* segment by Bhandari Automobiles.\n\nPlease choose the vehicles to continue?", "data": ["Small Trucks", "Trucks", "Vans & Buses"], "data_type": "button"}], "Small Trucks": [{"message": "Here are the popular small trucks to select your loved one.\n\nPlease choose the below vehicles to Explore?", "data": ["Ace", "Intra", "<PERSON><PERSON>"], "data_type": "button"}], "Ace": [{"message": "*Ace Variants Available*\nChoose a category to Explore specific models:", "data": ["Ace EV Models", "Ace Petrol Models", "Ace Diesel Models", "Ace CNG/Bi-Fuel Models", "🔁Back to main menu"], "data_type": "list"}], "Ace EV Models": [{"message": "*Ace - EV Models*\nSelect your preferred electric variant:", "data": ["Ace-Pro EV", "Ace EV 1000", "Ace EV", "🔁Back to Ace"], "data_type": "list"}], "Ace Petrol Models": [{"message": "*Ace - Petrol Models*\nSelect your preferred petrol variant:", "data": ["Ace-Pro Petrol", "Ace Gold Petrol", "Ace Flex Fuel", "🔁Back to Ace"], "data_type": "list"}], "Ace Diesel Models": [{"message": "*Ace - Diesel Models*\nSelect your preferred diesel variant:", "data": ["Ace Gold Diesel", "Ace Diesel", "Ace HT+", "🔁Back to Ace"], "data_type": "list"}], "Ace CNG/Bi-Fuel Models": [{"message": "*Ace – CNG & Bi-Fuel Models*\nSelect a CNG or bi-fuel model:", "data": ["Ace Gold CNG+", "Ace Pro Bi-Fuel", "Ace CNG 2.0", "Ace Z<PERSON>", "🔁Back to Ace"], "data_type": "list"}], "🔁Back to Ace": [{"message": "*Back to Ace*\n\nPlease select a category to continue:", "data": ["Ace EV Models", "Ace Petrol Models", "Ace Diesel Models", "Ace CNG & Bi-Fuel", "🔁Back to main menu"], "data_type": "list"}], "Intra": [{"message": "*Intra Variants Available*\nChoose a category to Explore specific models:", "data": ["Intra EV Models", "Intra Diesel Models", "🔁Back to main menu"], "data_type": "list"}], "Intra EV Models": [{"message": "*Intra - EV Models*\nSelect an electric variant:", "data": ["Intra EV", "🔁Back to Intra"], "data_type": "list"}], "Intra Diesel Models": [{"message": "*Intra - Diesel Models*\nChoose from the following:", "data": ["Intra V10", "Intra V20 Gold", "Intra V70 Gold", "Intra V30 Gold", "Intra V20 WINGER", "Intra V50 Gold", "🔁Back to Intra"], "data_type": "list"}], "🔁Back to Intra": [{"message": "*Back to Intra*\n\nPlease select a category to continue:", "data": ["Intra EV Models", "Intra Diesel Models", "🔁Back to main menu"], "data_type": "list"}], "Yodha": [{"message": "*<PERSON><PERSON> Variants Available*\nChoose a category to Explore specific models:", "data": ["<PERSON><PERSON> Models", "<PERSON><PERSON> Crew Models", "<PERSON><PERSON> Models", "🔁Back to main menu"], "data_type": "list"}], "Yodha CNG Models": [{"message": "*Yodha – CNG Models*\nChoose your CNG model:", "data": ["Yodha-CNG", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "Yodha Crew Models": [{"message": "*Yodha - Crew Models*\nChoose your configuration:", "data": ["<PERSON><PERSON>", "Yodha EX Crew Cab", "<PERSON>dha Crew Cab 4x2", "<PERSON>dha Crew Cab 4x4", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "Yodha Diesel Models": [{"message": "*<PERSON><PERSON> - Diesel Models*\nChoose from the following:", "data": ["Yodha 1200", "Yodha 2.0", "Yo<PERSON> 1700", "<PERSON><PERSON>", "Yodha EX Single Cab", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "🔁Back to Yodha": [{"message": "*Back to Yodha*\n\nPlease select a category to continue:", "data": ["<PERSON><PERSON> Models", "<PERSON><PERSON> Crew Models", "<PERSON><PERSON> Models", "🔁Back to main menu"], "data_type": "list"}], "Trucks": [{"message": "*Tata Truck Categories*\nPlease select a segment:", "data": ["Tata Prima Series", "Tata LPT Series", "Tata SFC Series", "Tata LPK Series", "Tata Signa Series", "Tata ULTRA Series", "🔁Back to main menu"], "data_type": "list"}], "Tata Prima Series": [{"message": "*Tata Prima Series*\nSelect a model to proceed:", "data": ["PRIMA 2830.K SRT", "PRIMA 2830.K HRT", "PRIMA 2830K REPTO", "PRIMA 3530.K SRT", "PRIMA 3530.K HRT", "PRIMA 5530.S", "🔙to Trucks"], "data_type": "list"}], "Tata LPT Series": [{"message": "*Tata LPT Series*\nChoose a category:", "data": ["LPT 700-1000 Series", "LPT 1100-1700 Series", "🔙to Trucks"], "data_type": "button"}], "LPT 700-1000 Series": [{"message": "*Tata LPT 700-1000 Series*\nSmall capacity trucks. Select a model:", "data": ["TATA LPT 709G", "TATA LPT 710", "TATA LPT 712", "TATA LPT 912", "TATA LPT 1009G", "TATA LPT 1012", "🔙to LPT Series"], "data_type": "list"}], "LPT 1100-1700 Series": [{"message": "*Tata LPT 1100-1700 Series*\nMedium duty trucks. Select a model:", "data": ["TATA LPT 1109G", "TATA LPT 1112", "TATA LPT 1212", "TATA LPT 1216", "TATA LPT 1416", "TATA LPT 1612G", "TATA LPT 1616", "Tata LPT 1616 NT", "🔙to LPT Series"], "data_type": "list"}], "Tata SFC Series": [{"message": "*Tata SFC Series*\nSelect a model to proceed:", "data": ["TATA SFC 712", "TATA SFC 407 GOLD", "🔙to Trucks"], "data_type": "list"}], "Tata LPK Series": [{"message": "*Tata LPK Series*\nSelect a model to proceed:", "data": ["TATA LPK 1416", "TATA LPK 2821K FE+ RMC", "🔙to Trucks"], "data_type": "list"}], "Tata Signa Series": [{"message": "*Tata Signa Series*\nChoose a category:", "data": ["Signa 1900-3100", "Signa 3500-5500", "🔙to Trucks"], "data_type": "button"}], "Signa 1900-3100": [{"message": "*Tata Signa 1900-3100 Series*\nMedium duty trucks. Select a model:", "data": ["TATA SIGNA 1923.K", "TATA SIGNA 2821.T", "TATA SIGNA 2823.T", "TATA SIGNA 2830K REPTO", "TATA SIGNA 3125.T", "🔙to Signa Series"], "data_type": "list"}], "Signa 3500-5500": [{"message": "*Tata Signa 3500-5500 Series*\nHeavy duty trucks. Select a model:", "data": ["TATA SIGNA 3525.T", "TATA SIGNA 4021.S", "TATA SIGNA 4025.S", "TATA SIGNA 4225.T", "TATA SIGNA 4623.S", "TATA SIGNA 4830.T", "TATA SIGNA 4830.TKFBV", "TATA SIGNA 4930.T", "TATA SIGNA 5530.S", "🔙to Signa Series"], "data_type": "list"}], "Tata ULTRA Series": [{"message": "*Tata ULTRA Series*\nSelect a model to proceed:", "data": ["TATA ULTRA T.6", "TATA ULTRA T.7", "TATA ULTRA T.9", "TATA ULTRA T.11", "TATA ULTRA T.16", "TATA ULTRA T.18", "TATA ULTRA T.19", "TATA ULTRA K.14", "🔙to Trucks"], "data_type": "list"}], "🔁Back to Trucks": [{"message": "*Back to Tata Trucks*\nSelect a segment to continue:", "data": ["Tata Prima Series", "Tata LPT Series", "Tata SFC Series", "Tata LPK Series", "Tata Signa Series", "Tata ULTRA Series", "🔁Back to main menu"], "data_type": "list"}], "Vans & Buses": [{"message": "Here are the popular *Tata Vans & Buses* segment by Bhandari Automobiles.\n\nPlease choose the vehicles to continue?", "data": ["<PERSON><PERSON>", "Buses"], "data_type": "button"}], "Vans": [{"message": "Here are the popular *Tata Vans* segment by Bhandari Automobiles 👇.", "data": ["School Vans", "Tourist <PERSON><PERSON>", "Ambulance Vans", "🔁Back to main menu"], "data_type": "list"}], "🔙to Van Models": [{"message": "*Back to Van Models*\nSelect a category to Explore:", "data": ["School Vans", "Tourist <PERSON><PERSON>", "Ambulance Vans", "🔁Back to main menu"], "data_type": "list"}], "School Vans": [{"message": "*Tata School Van Series*\nSelect a Model to Explore:", "data": ["MAGIC EXP YELLOW 10S", "MAGIC EXPRESS 10S", "MAGIC MANTRA 10S", "WINGER 21S 35WB SCH HR", "WINGER RDE 21S 35WB P2", "WINGER FL 3200 13S AC", "WINGER FL SK 3200 18S", "WINGER FL SK 3488 21S", "🔙to Van Models", "🔁Back to main menu"], "data_type": "list"}], "Tourist Vans": [{"message": "*Tata Tourist Van Types*\nSelect a category to Explore:", "data": ["High Roof Vans", "Flat Roof Vans", "🔙to Van Models", "🔁Back to main menu"], "data_type": "list"}], "High Roof Vans": [{"message": "*These are the new High Roof Van models*\nSelect a Model to Explore:", "data": ["MAGIC EXPRESS CNG", "FL 35 HR PAS 15S SHL", "FL 3488 HR 12S AC", "FL 35 HR PAS 15S", "FL 3488 HR 12S", "FL 3488 HR 15S AC", "🔙to Tourist", "🔁Back to main menu"], "data_type": "list"}], "Flat Roof Vans": [{"message": "*These are the new Flat Roof Van models*\nSelect a Model to Explore:", "data": ["FL 3200 FR 12S AC", "RDE 9S 32WB PAS FR", "FL 3200 FR 13S NAC", "FL 3200 FR 13S AC", "FL 3200 FR 9S AC", "FL 3200 FR 12S", "WINGER 12S", "🔙to Tourist", "🔁Back to main menu"], "data_type": "list"}], "🔙Back to Tourist": [{"message": "*Back to Tourist <PERSON>s*\nSelect a category to Explore:", "data": ["High Roof Vans", "Flat Roof Vans", "🔙to Van Models", "🔁Back to main menu"], "data_type": "list"}], "Ambulance Vans": [{"message": "*Tata Ambulance Vans*\nSelect a Model to Explore:", "data": ["BS6 Phase 2 Models", "AC Standard Models", "🔙to Van Models", "🔁Back to main menu"], "data_type": "list"}], "BS6 Phase 2 Models": [{"message": "*These are the newer emission-standard BS6 Phase 2 models*\nSelect a category to Explore:", "data": ["MAGIC AMBULANCE", "FL AMB-B 35 8+P P2", "FL AMB 3200 7+P P2", "FL AMB 35SH 10+P P2", "FL AMB 32SH 10+P P2", "AMB TYPE C 7+P AC SI", "🔙to Ambulances", "🔁Back to main menu"], "data_type": "list"}], "AC Standard Models": [{"message": "*These are the regular or AC-equipped versions*\nSelect a category to Explore:", "data": ["FL AMB B 3200 7+P AC", "FL AMB 32SH 10+P AC", "FL AMB 35SH 10+P AC", "FL AMB B 35 8+P AC", "AMB TYPE D BS6 7+P A", "AMBULANCE 3200", "🔙to Ambulances", "🔁Back to main menu"], "data_type": "list"}], "🔙Back to Ambulances": [{"message": "*Back to Ambulances*\nSelect a Model category to Explore:", "data": ["BS6 Phase 2 Models", "AC Standard Models", "🔙to Van Models", "🔁Back to main menu"], "data_type": "list"}], "Buses": [{"message": "*Tata Buses*\nSelect a Bus model to Explore:", "data": ["School Buses", "Staff Contract", "🔁Back to main menu"], "data_type": "list"}], "School Buses": [{"message": "*Tata School Buses*\nSelect a Bus model to Explore:", "data": ["LPO Series", "LP Series", "CITYRIDE SKL Series", "ULTRA BUS", "STARBUS Series", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "Staff Contract": [{"message": "*Tata Staff & Contract Buses*\nSelect a Bus model to Explore:", "data": ["EV 9 to 12 Seater", "12 to 24 Seater Bus", "32 to 40 Seater Bus", "42 to 55 Seater Bus", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "EV 9 to 12 Seater": [{"message": "*Tata EV Bus Models *\nSelect a EV Bus model to Explore:", "data": ["ULTRA 9 9 EV", "STARBUS 4 12 EV", "STARBUS 9 12 EV", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "12 to 24 Seater Bus": [{"message": "*Tata 12-24 Seater Bus Models*\nSelect a bus model to Explore:", "data": ["12+D LP410 29", "16+D LP410 29", "20+D LP 410 33", "24+D LP412 36", "24+D LP410 36G", "24+D AC 410 36G", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "32 to 40 Seater Bus": [{"message": "*Tata 32-40 Seater Bus Models*\nSelect a bus model to Explore:", "data": ["32+D AC LP716 45", "32+D LP712 45", "34+D LPO8.6 44", "40+D AC LP916 52", "40+D LP916 52", "40+D AC LP913 52G", "40+D LP910 52G", "40+D LP812 52", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "42 to 55 Seater Bus": [{"message": "*Tata 42-55 Seater Bus Models*\nSelect a bus model to Explore:", "data": ["ULTRA 9 7", "42+D AC LPO11.6 54", "44+D LPO11.6 54", "50+D AC LPO11.6 54", "50+D LPO11.6 54", "50+D LP916 52", "55+D LPO11.6 54", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "LPO Series": [{"message": "*Tata LPO CNG & Diesel Series Buses*\nSelect a Bus Chassis model to Explore:", "data": ["LPO 11.6 52 CNG", "LPO 1316 57 DIESEL", "LPO 1316 54 DIESEL", "LPO 11.6 54 DIESEL", "LPO 8.6 44 DIESEL", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "LP Series": [{"message": "*Tata LP Series Buses*\nSelect a Bus Chassis model to Explore:", "data": ["LP DIESEL VARIANTS", "LP CNG VARIANTS", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "LP DIESEL VARIANTS": [{"message": "*Tata LP DIESEL VARIANT Buses*\nSelect a Bus Chassis model to Explore:", "data": ["LP 410 31 DIESEL", "LP 410 33 DIESEL", "LP 412 36 DIESEL", "LP 712 39 DIESEL", "LP 712 45 DIESEL", "LP 716 45 DIESEL", "LP 812 52 DIESEL", "LP 916 52 DIESEL", "🔙to Buses"], "data_type": "list"}], "LP CNG VARIANTS": [{"message": "*Tata LP CNG VARIANT Buses*\nSelect a Bus Chassis model to Explore:", "data": ["LP 410/36 CNG", "LP 910/52 CNG", "LP 913/52 CNG", "🔙to Buses"], "data_type": "list"}], "CITYRIDE SKL Series": [{"message": "*Tata CITYRIDE SKL Series Buses*\nSelect a Bus model to Explore:", "data": ["SKL 410-412 Series", "SKL 712-812 Series", "🔙to Buses"], "data_type": "list"}], "SKL 410-412 Series": [{"message": "*Tata SKL 410-412 Series Buses*\nSelect a Bus model to Explore:", "data": ["SKL 19+A+D LP410 29", "SKL 19+B+D LP410 30", "SKL 19+C+D LP410 31", "SKL 27+A+D LP410 33", "SKL 29+A+D LP412 36", "SKL 35+A+D LP412 36", "SKL 41+A+D LP412 36", "🔙to Buses"], "data_type": "list"}], "SKL 712-812 Series": [{"message": "*Tata SKL 712-812 Series Buses*\nSelect a Bus model to Explore:", "data": ["SKL 37+A+D LP712 45", "SKL 45+A+D LP712 45", "SKL 53+A+D LP712 45", "SKL 41+A+D LP812 52", "SKL 50+A+D LP812 52", "SKL 59+A+D LP812 52", "🔙to Buses"], "data_type": "list"}], "ULTRA BUS": [{"message": "*Tata Ultra Buses*\nSelect a ULTRA BUS model to Explore:", "data": ["19+A+D LP410 29", "58+A+D LPO11.6 54", "🔙to Buses"], "data_type": "list"}], "STARBUS Series": [{"message": "*Tata STARBUS Series Buses*\nSelect a STARBUS model to Explore:", "data": ["STARBUS PRIME", "STARBUS SKL", "🔙to Buses", "🔁Back to main menu"], "data_type": "list"}], "STARBUS PRIME": [{"message": "*Tata Starbus Prime Buses*\nSelect a Starbus Seater option to Explore:", "data": ["Starbus 19-46 Seater", "Starbus 50-59 Seater", "🔙to Buses"], "data_type": "list"}], "Starbus 19-46 Seater": [{"message": "*Tata Starbus 19-46 Seater Buses*\nSelect a Bus model to Explore:", "data": ["BUS 19+A+D LP410 29", "BUS 19+B+D LP 410 30", "BUS 38+A+D LP412 36", "BUS 34+A+D LP412 36", "BUS 34+A+D AC410 36G", "BUS 38+A+D LP410 36G", "BUS 34+A+D LP410 36G", "BUS 46+A+D AC 712 45", "BUS 46+A+D LP712 45", "🔙to Buses"], "data_type": "list"}], "Starbus 50-59 Seater": [{"message": "*Tata Starbus 50-59 Seater Buses*\nSelect a Starbus model to Explore:", "data": ["BUS 53+A+D LP712 45", "BUS 51+A+D 916 52", "BUS 51+A+D AC 916 52", "BUS 51+A+D LP916 52", "BUS 51+A+D AC 913 52G", "BUS 59+A+D LP812 52", "BUS 51+A+D LP812 52", "BUS 50+D AC LP916 52", "🔙to Buses"], "data_type": "list"}], "🔙to Buses": [{"message": "*Tata Buses*\nSelect a Bus model to Explore:", "data": ["School Buses", "Staff & Contract", "🔁Back to main menu"], "data_type": "list"}], "Purchase Accessories": [{"message": "*Purchase Accessories*\n\nPlease choose a category to Explore available accessories:", "data": ["Interior Accessories", "Exterior Accessories", "Car Care Products", "🔁Back to main menu"], "data_type": "list"}], "Interior Accessories": [{"message": "*Interior Accessories*\n\nChoose from premium options for your car's comfort and style:", "data": ["Seat Covers", "Floor Mats", "Sun Shades", "Car Perfume", "🔁Back to Accessories"], "data_type": "list"}], "Exterior Accessories": [{"message": "*Exterior Accessories*\n\nEnhance your car's look and safety with these options:", "data": ["Body Cover", "Door Visor", "Side Moulding", "<PERSON>d <PERSON>laps", "🔁Back to Accessories"], "data_type": "list"}], "Car Care Products": [{"message": "*Car Care Products*\n\nMaintain your car's shine and health with these products:", "data": ["Shampoo & Polish", "Dashboard Cleaner", "Microfiber Cloth", "<PERSON><PERSON><PERSON> Remover", "🔁Back to Accessories"], "data_type": "list"}], "Used Cars": [{"message": "*Used Cars*, powered by Maruti Suzuki and trusted by Bhandari Automobiles, is India's largest certified platform for buying, selling, and exchanging pre‑owned cars—designed to offer transparency, trust, and total peace of mind 🚗✅.\n\n🛠️ Why Choose True Value?\n\n🔍 120-Point Quality Check - Every car undergoes a rigorous multi-stage inspection by trained Maruti engineers.", "data": ["Buy Pre-Owned Car", "Sell your Car", "🔁Back to main menu"], "data_type": "button"}], "Buy Pre-Owned Car": [{"message": "Looking to buy a used car that feels like new? With Maruti Suzuki True Value, you get a certified, reliable, and thoroughly inspected pre-owned vehicle—backed by the trust of India’s No.1 car brand ✅🔧.\n\n🌟 Why Buy from Maruti Suzuki True Value?\n\n🔍 120-Point Quality Check\n🛠️ Refurbished with Genuine Maruti Parts\n📃 Certified Pre-Owned Badge.", "data": ["Sell your Car", "🔁Back to main menu"], "data_type": "button"}], "Sell your Car": [{"message": "Want to sell your old car without the stress and uncertainty? With Maruti Suzuki True Value, you get a transparent, quick, and fair selling process—backed by India's most trusted automobile brand 💼🚗💯.\n\n🌟 Why Sell with True Value?\n\n📲 Instant Online Evaluation\n🧑‍🔧 Expert Inspection\n📈 Best Market Price\n💬 No Middlemen or Hidden Charges\n🔁 Easy Exchange Option\n🧾 Assistance with Documentation\n\n👇Please click on the link below to Explore more:\nhttps://www.marutisuzukitruevalue.com/sell-cars", "data": ["Buy Pre-Owned Car", "🔁Back to main menu"], "data_type": "button"}], "Explore More": [{"message": "Which of the below options interests you to Explore?", "data": ["Insurance Renewal", "Contact Us", "🔁Back to main menu"], "data_type": "button"}], "Insurance Renewal": [{"message": "Welcome to *Maruti Suzuki Insurance Broking*, your One Stop Shop for All Insurance Needs.\n\n*Key highlights:*\n\n☑ Dealer Assisted Towing facility\n☑ Dedicated Customer Care\n☑ Instant Policy Issuance\n☑ Near Cash‑less Accident Repairs Pan‑India\n☑ Fair and transparent Claim settlement", "data": ["Policy Renewal", "Policy Download", "Claim Process", "🔁Back to main menu"], "data_type": "list"}], "Policy Renewal": [{"message": "Please click the below link for Policy Renewal 👇\n\nhttps://www.marutisuzukiinsurance.com/Renew-Car-Insurance-Policy.aspx", "data": ["Policy Download", "Claim Process", "🔁Back to main menu"], "data_type": "button"}], "Policy Download": [{"message": "Please click the below link for Policy Download 👇\n\nhttps://www.marutisuzukiinsurance.com/PolicyDownload.aspx", "data": ["Policy Renewal", "Claim Process", "🔁Back to main menu"], "data_type": "button"}], "Claim Process": [{"message": "Follow the steps below for Claim Process:\n\n*Step 1:* Claim Intimation and Estimation\n*Step 2:* Surveyor Appointment and Loss Assessment\n*Step 3:* Commencement of Repairs Post Approvals\n*Step 4:* Vehicle Re‑Inspection and Delivery", "data": ["Policy Renewal", "Policy Download", "🔁Back to main menu"], "data_type": "button"}], "Contact Us": [{"message": "📞 Thank you for Contacting Bhandari Automobiles.\n\nHere are the key locations and contacts:\n\n📍 Arena – Newtown, Kolkata\n📞 +91 80716 46215 | 🕘 10 AM – 7 PM\n🌐 arenaofnewtownrajarhat.com\n\n📍 Arena – Nibra (Salap), Howrah\n📞 +91 80625 13689 | 🕘 10 AM – 7 PM\n🌐 arenaofnibra.com", "data": ["🔁Back to main menu"], "data_type": "button"}], "🔁Back to Accessories": [{"message": "*Purchase Accessories*\n\nPlease choose a category to Explore available accessories:", "data": ["Interior Accessories", "Exterior Accessories", "Car Care Products", "🔁Back to main menu"], "data_type": "list"}], "Book a Service": [{"message": "🔧 *Book Your Service Appointment*\n\nWe provide comprehensive service for all Maruti Suzuki and Tata Commercial vehicles.\n\n🛠️ *Our Services Include:*\n• Regular Maintenance & Servicing\n• Repairs & Diagnostics\n• Genuine Parts Replacement\n• Insurance Claims Support.", "data": [{"type": "form", "title": "Book a Service", "form_id": "24211602828534114", "form_name": "Book a Service"}], "data_type": "form"}], "Book a Insurance": [{"message": "🛡️ *Book Your Insurance Policy*\n\nWe provide comprehensive insurance solutions for all Maruti Suzuki and Tata Commercial vehicles.\n\n🛡️ *Our Insurance Services Include:*\n• Comprehensive Motor Insurance\n• Third Party Insurance\n• Zero Depreciation Cover\n• Add-on Covers & Benefits\n• Claim Support & Assistance.", "data": [{"type": "form", "title": "Book a Insurance", "form_id": "24211602828534115", "form_name": "Book a Insurance"}], "data_type": "form"}], "Request a Call Back": [{"message": "📝 *Disclaimer:* Thank you for requesting a callback. Our dealer will contact you SOON.", "data": [], "data_type": "text"}], "Book a Test Drive": [{"message": "📝 *Disclaimer:* Thank you for booking a test drive. Our dealer will contact you SOON.", "data": [], "data_type": "text"}], "Live Talk": [{"message": "🚗 *Welcome to Maruti Suzuki Live Support!* \n\nYou can chat with our bot for instant assistance or connect with a live agent for detailed help.", "data": ["Chat With Bot", "Talk With Live Agent", "🔁Back to main menu"], "data_type": "button"}]}