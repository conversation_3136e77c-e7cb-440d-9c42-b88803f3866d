import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleSheetsManager:
    """
    Google Sheets integration for storing Meta form submissions
    """
    
    def __init__(self):
        self.service = None
        self.spreadsheet_id = os.getenv('GOOGLE_SHEETS_ID')
        self.credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', 'credentials.json')
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize Google Sheets API service"""
        try:
            # Define the scope
            SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
            
            # Load credentials
            if os.path.exists(self.credentials_path):
                credentials = Credentials.from_service_account_file(
                    self.credentials_path, scopes=SCOPES
                )
            else:
                # Try to load from environment variable (JSON string)
                creds_json = os.getenv('GOOGLE_CREDENTIALS_JSON')
                if creds_json:
                    credentials_info = json.loads(creds_json)
                    credentials = Credentials.from_service_account_info(
                        credentials_info, scopes=SCOPES
                    )
                else:
                    logger.error("❌ Google credentials not found")
                    return
            
            # Build the service
            self.service = build('sheets', 'v4', credentials=credentials)
            logger.info("✅ Google Sheets service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Sheets service: {e}")
            self.service = None
    
    def create_sheet_if_not_exists(self, sheet_name: str) -> bool:
        """
        Create a new sheet if it doesn't exist
        
        Args:
            sheet_name (str): Name of the sheet to create
            
        Returns:
            bool: True if sheet exists or was created successfully
        """
        try:
            if not self.service or not self.spreadsheet_id:
                logger.error("❌ Google Sheets service not initialized or spreadsheet ID missing")
                return False
            
            # Get existing sheets
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()
            
            existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]
            
            if sheet_name in existing_sheets:
                logger.info(f"✅ Sheet '{sheet_name}' already exists")
                return True
            
            # Create new sheet
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }
            
            self.service.spreadsheets().batchUpdate(
                spreadsheetId=self.spreadsheet_id,
                body=request_body
            ).execute()
            
            logger.info(f"✅ Created new sheet: '{sheet_name}'")
            return True
            
        except HttpError as e:
            # Check if error is because sheet already exists
            if "already exists" in str(e):
                logger.info(f"✅ Sheet '{sheet_name}' already exists (caught in exception)")
                return True
            else:
                logger.error(f"❌ Error creating sheet '{sheet_name}': {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Unexpected error creating sheet '{sheet_name}': {e}")
            return False
    
    def setup_sheet_headers(self, sheet_name: str, headers: List[str]) -> bool:
        """
        Setup headers for a sheet if they don't exist
        
        Args:
            sheet_name (str): Name of the sheet
            headers (List[str]): List of header names
            
        Returns:
            bool: True if headers were set up successfully
        """
        try:
            if not self.service or not self.spreadsheet_id:
                return False
            
            # Check if headers already exist
            range_name = f"{sheet_name}!A1:Z1"
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=range_name
            ).execute()
            
            existing_headers = result.get('values', [[]])
            
            if existing_headers and existing_headers[0]:
                logger.info(f"✅ Headers already exist in sheet '{sheet_name}'")
                return True
            
            # Add headers
            values = [headers]
            body = {
                'values': values
            }
            
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=f"{sheet_name}!A1",
                valueInputOption='RAW',
                body=body
            ).execute()
            
            logger.info(f"✅ Headers added to sheet '{sheet_name}': {headers}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up headers for sheet '{sheet_name}': {e}")
            return False
    
    def append_form_data(self, sheet_name: str, form_data: Dict[str, Any], form_type: str) -> bool:
        """
        Append form data to the specified sheet
        
        Args:
            sheet_name (str): Name of the sheet to append to
            form_data (Dict[str, Any]): Form data from Meta
            form_type (str): Type of form (e.g., 'contact_form', 'test_drive_form')
            
        Returns:
            bool: True if data was appended successfully
        """
        try:
            if not self.service or not self.spreadsheet_id:
                logger.error("❌ Google Sheets service not initialized")
                return False
            
            # Create sheet if it doesn't exist
            if not self.create_sheet_if_not_exists(sheet_name):
                return False
            
            # Define headers based on form type
            headers = self._get_headers_for_form_type(form_type)
            
            # Setup headers if needed
            if not self.setup_sheet_headers(sheet_name, headers):
                return False
            
            # Prepare row data
            row_data = self._prepare_row_data(form_data, form_type, headers)
            
            # Append data
            values = [row_data]
            body = {
                'values': values
            }
            
            range_name = f"{sheet_name}!A:Z"
            
            result = self.service.spreadsheets().values().append(
                spreadsheetId=self.spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body=body
            ).execute()
            
            logger.info(f"✅ Form data appended to sheet '{sheet_name}': {result.get('updates', {}).get('updatedRows', 0)} rows added")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error appending form data to sheet '{sheet_name}': {e}")
            return False
    
    def _get_headers_for_form_type(self, form_type: str) -> List[str]:
        """
        Get appropriate headers based on form type

        Args:
            form_type (str): Type of form

        Returns:
            List[str]: List of header names
        """
        base_headers = [
            'Timestamp',
            'Form Type',
            'Name',
            'Email',
            'Mobile Number',
            'Alternate Number',
            'Message'
        ]

        if form_type in ['service_form', 'book_service']:
            return base_headers + [
                'Service Type',
                'Car Model',
                'Vehicle Number',
                'Preferred Date',
                'Preferred Time',
                'Service Location',
                'Issue Description',
                'Source'
            ]
        elif form_type in ['insurance_form', 'book_insurance']:
            return base_headers + [
                'Insurance Type',
                'Car Model',
                'Vehicle Number',
                'Current Insurer',
                'Policy Expiry Date',
                'Coverage Required',
                'Annual Premium Budget',
                'Source'
            ]
        elif form_type == 'test_drive_form':
            return base_headers + [
                'Preferred Car',
                'Preferred Date',
                'Preferred Time',
                'Location'
            ]
        elif form_type == 'contact_form':
            return base_headers + [
                'Subject',
                'Inquiry Type',
                'Source'
            ]
        else:
            # Generic form headers
            return base_headers + [
                'Additional Info',
                'Source',
                'Campaign'
            ]
    
    def _prepare_row_data(self, form_data: Dict[str, Any], form_type: str, headers: List[str]) -> List[str]:
        """
        Prepare row data based on headers and form data

        Args:
            form_data (Dict[str, Any]): Form data from Meta
            form_type (str): Type of form
            headers (List[str]): Sheet headers

        Returns:
            List[str]: Row data matching headers
        """
        row_data = []

        for header in headers:
            if header == 'Timestamp':
                row_data.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            elif header == 'Form Type':
                row_data.append(form_type)
            elif header == 'Name':
                row_data.append(form_data.get('name', form_data.get('full_name', '')))
            elif header == 'Email':
                row_data.append(form_data.get('email', ''))
            elif header == 'Mobile Number':
                row_data.append(form_data.get('Mobile Number', form_data.get('mobile_number', '')))
            elif header.strip().lower() in ['alternate_number', 'alternate number', 'alternative number']:
                row_data.append(
                    form_data.get('alternate_number') or
                    form_data.get('alternative_number') or
                    form_data.get('Alternate Number') or
                    form_data.get('Alternative Number') or
                    form_data.get('alt_number') or
                    form_data.get('alt_phone') or
                    form_data.get('phone_alt') or
                    ''
                )


            elif header.lower() == 'message':
                row_data.append(form_data.get('message', form_data.get('comments', '')))

            # Service form specific fields
            elif header == 'Service Type':
                row_data.append(form_data.get('service_type', form_data.get('service_category', '')))
            elif header == 'Car Model':
                row_data.append(form_data.get('car_model', form_data.get('vehicle_model', '')))
            elif header == 'Vehicle Number':
                row_data.append(form_data.get('registration_number', form_data.get('reg_number', form_data.get('vehicle_number', ''))))
            elif header == 'Service Location':
                row_data.append(form_data.get('service_location', form_data.get('location', '')))
            elif header == 'Issue Description':
                row_data.append(form_data.get('issue_description', form_data.get('problem_description', '')))

            # Insurance form specific fields
            elif header == 'Insurance Type':
                row_data.append(form_data.get('insurance_type', form_data.get('coverage_type', '')))
            elif header == 'Current Insurer':
                row_data.append(form_data.get('current_insurer', form_data.get('existing_insurer', '')))
            elif header == 'Policy Expiry Date':
                row_data.append(form_data.get('policy_expiry_date', form_data.get('expiry_date', '')))
            elif header == 'Coverage Required':
                row_data.append(form_data.get('coverage_required', form_data.get('coverage_needed', '')))
            elif header == 'Annual Premium Budget':
                row_data.append(form_data.get('annual_premium_budget', form_data.get('budget', '')))

            # Common fields
            elif header == 'Preferred Car':
                row_data.append(form_data.get('car_model', form_data.get('preferred_car', '')))
            elif header == 'Preferred Date':
                row_data.append(form_data.get('preferred_date', ''))
            elif header == 'Preferred Time':
                row_data.append(form_data.get('preferred_time', ''))
            elif header == 'Location':
                row_data.append(form_data.get('location', form_data.get('dealership', '')))
            elif header == 'Subject':
                row_data.append(form_data.get('subject', ''))
            elif header == 'Inquiry Type':
                row_data.append(form_data.get('inquiry_type', ''))
            elif header == 'Source':
                row_data.append(form_data.get('source', 'Meta Form'))
            elif header == 'Additional Info':
                row_data.append(form_data.get('additional_info', ''))
            elif header == 'Campaign':
                row_data.append(form_data.get('campaign', ''))
            else:
                # Try to find matching field in form_data
                row_data.append(form_data.get(header.lower().replace(' ', '_'), ''))

        return row_data

# Global instance
sheets_manager = GoogleSheetsManager()

def get_sheet_name_for_form_type(form_type: str) -> str:
    """
    Get the appropriate sheet name based on form type

    Args:
        form_type (str): Type of form

    Returns:
        str: Sheet name to use
    """
    sheet_mapping = {
        'service_form': 'Book_a_Service',
        'book_service': 'Book_a_Service',
        'Book_a_Service': 'Book_a_Service',  # Direct mapping for form name
        'book_a_service': 'Book_a_Service',   # Alternative naming
        'insurance_form': 'Book_a_Insurance',
        'book_insurance': 'Book_a_Insurance',
        'Book_a_Insurance': 'Book_a_Insurance',  # Direct mapping for form name
        'book_a_insurance': 'Book_a_Insurance'   # Alternative naming
    }

    return sheet_mapping.get(form_type, 'Book_a_Service')

def store_meta_form_data(form_data: Dict[str, Any], form_type: str, sheet_name: str = None) -> bool:
    """
    Store Meta form data in Google Sheets with automatic sheet selection

    Args:
        form_data (Dict[str, Any]): Form data from Meta webhook
        form_type (str): Type of form (e.g., 'service_form', 'insurance_form')
        sheet_name (str): Optional specific sheet name (if None, auto-determined)

    Returns:
        bool: True if data was stored successfully
    """
    try:
        # Auto-determine sheet name if not provided
        if sheet_name is None:
            sheet_name = get_sheet_name_for_form_type(form_type)

        logger.info(f"📝 Storing Meta form data: {form_type} -> {sheet_name}")
        return sheets_manager.append_form_data(sheet_name, form_data, form_type)
    except Exception as e:
        logger.error(f"❌ Error storing Meta form data: {e}")
        return False


def store_test_drive_callback_data(request_data):
    """
    Store test drive and callback request data in Google Sheets

    Args:
        request_data (dict): Request data containing user_id, request_type, car_name, etc.

    Returns:
        bool: True if successfully stored, False otherwise
    """
    try:
        # Use separate sheets based on request type
        if request_data['request_type'] == 'test_drive':
            sheet_name = 'Book_a_Testdrive'
        elif request_data['request_type'] == 'call_back':
            sheet_name = 'Request_a_callback'
        else:
            sheet_name = 'Book_a_Service'  # Fallback

        # Prepare data for the sheet
        form_data = {
            'name': f"{request_data['request_type'].title()} Request",
            'mobile_number': request_data['phone_number'],
            'vehicle_number': request_data.get('car_name', 'Not specified'),
            'service_type': request_data['request_type'].upper(),
            'location': 'WhatsApp Bot',
            'email': 'N/A',
            'submission_timestamp': request_data['timestamp'],
            'status': request_data.get('status', 'Pending')
        }

        logger.info(f"📝 Storing {request_data['request_type']} request: {request_data['phone_number']} -> {sheet_name}")

        # Use the existing form data storage mechanism
        success = sheets_manager.append_form_data(sheet_name, form_data, 'service_form')

        if success:
            logger.info(f"✅ {request_data['request_type'].title()} request stored successfully")
        else:
            logger.error(f"❌ Failed to store {request_data['request_type']} request")

        return success

    except Exception as e:
        logger.error(f"❌ Error storing test drive/callback data: {e}")
        return False
