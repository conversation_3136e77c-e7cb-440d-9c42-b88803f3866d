import re
import os
import logging
import time
import sys
import json
import hashlib
import hmac
from datetime import datetime
from dotenv import load_dotenv
from flask import Flask, jsonify, request, abort
from flask_cors import CORS
import google.generativeai as genai
# Removed unused import: from cars import arena_data
from redis_loading import CAR_DATA_REGISTRY, CAR_NAME_MAPPINGS, MAIN_MENU_DATA, create_enhanced_system_prompt, get_car_data_from_redis_by_name, load_information_file, load_system_prompt
from tools_def import TOOL_DEF
import utility
from utility import WHATSAPP_CONFIG, format_all_cars_message, format_car_details_from_data
from car_data_manager import car_data_manager, get_car_data
from sheet import store_meta_form_data
from bhandari_database import (
    parse_user_id, extract_token_counts,
    sync_insert_chat_message, sync_insert_user_interaction,
    sync_insert_service_booking, sync_create_live_chat_session,
    sync_get_chat_history
)

# Simple in-memory session storage for tracking user's last viewed car
USER_CAR_CONTEXT = {}

def update_user_car_context(user_id, car_name):
    """Update the user's car context when they view a car"""
    if user_id and car_name and car_name != 'Not specified':
        USER_CAR_CONTEXT[user_id] = car_name
        logger.info(f"🔄 Updated car context for user {user_id}: {car_name}")

def get_user_car_context(user_id):
    """Get the user's last viewed car"""
    return USER_CAR_CONTEXT.get(user_id, 'Not specified')

def get_price_key_for_car(car_data):
    """
    Get the correct price key for a car's variant data
    Handles different naming conventions across JSON files
    """
    possible_price_keys = [
        'Ex-showroom Price',      # Most Arena cars (hyphen)
        'Ex-showroom Price',      # Some cars (en-dash)
        'Ex-Showroom Prices',     # Some NEXA cars (hyphen + plural)
        'Ex-Showroom Prices',     # Variation with en-dash and plural
        'Variants',               # Alternative naming
        'Price List'              # Alternative naming
    ]

    for key in possible_price_keys:
        if key in car_data:
            return key

    return None

def get_car_variants_unified(car_data):
    """
    Get car variants with unified handling of different price key formats
    Works with both old JSON format and new arena_data.py/nexa_data.py format
    """
    # Try to find variants key in new format (from arena_data.py/nexa_data.py)
    for key in car_data.keys():
        if key.endswith(' variants'):
            variants = car_data[key]
            if isinstance(variants, list):
                return variants

    # Fallback to old format
    price_key = get_price_key_for_car(car_data)

    if not price_key:
        return []

    price_data = car_data.get(price_key, [])
    if not price_data or len(price_data) == 0:
        return []

    variants = price_data[0].get('data', [])
    return variants

def get_car_data_by_name(car_name):
    """Get car data by name with Redis as primary source, fallback to registry and name mappings"""
    # First try to get data from Redis
    redis_data = get_car_data_from_redis_by_name(car_name)
    if redis_data:
        return redis_data

    # Fallback to registry lookup
    if car_name in CAR_DATA_REGISTRY:
        return CAR_DATA_REGISTRY[car_name]

    # Try name mapping
    normalized_name = car_name.lower().strip()
    if normalized_name in CAR_NAME_MAPPINGS:
        mapped_name = CAR_NAME_MAPPINGS[normalized_name]
        # Try Redis first for mapped name
        redis_data = get_car_data_from_redis_by_name(mapped_name)
        if redis_data:
            return redis_data
        # Fallback to registry
        return CAR_DATA_REGISTRY.get(mapped_name, {})

    return {}

def is_specific_model_name(name):
    """
    Check if a name represents a specific vehicle model (not a category)

    Args:
        name (str): Name to check

    Returns:
        bool: True if it's a specific model, False if it's a category
    """
    if not name or name == 'Not specified':
        return False

    # Categories that are NOT specific models
    categories = [
        'Main Menu', 'Arena Cars', 'NEXA Cars', 'Tata Commercials',
        'Small Trucks', 'Trucks', 'Vans/Buses', 'Ace', 'Intra', 'Yodha',
        'Ace EV Models', 'Ace Petrol Models', 'Ace Diesel Models', 'Ace CNG/Bi-Fuel Models',
        'Yodha CNG Models', 'Yodha Crew Models', 'Yodha Diesel Models',
        'Tata Prima Series', 'Tata LPT Series', 'Tata SFC Series', 'Tata LPK Series',
        'Tata Signa Series', 'Tata ULTRA Series', 'School Vans', 'Tourist Vans',
        'Ambulance Vans', 'High Roof Vans', 'Flat Roof Vans', 'BS6 Phase 2 Models',
        'AC Standard Models', 'Buses', 'School Buses', 'Staff Contract',
        'LPO Series', 'LP Series', 'CITYRIDE SKL Series', 'ULTRA BUS', 'STARBUS Series'
    ]

    return name not in categories

def get_model_name_from_context(user_id, fallback_name=None):
    """
    Get the most specific model name from user context or current interaction

    Args:
        user_id (str): WhatsApp user ID
        fallback_name (str): Fallback name if no context found

    Returns:
        str: Most specific model name available
    """
    try:
        # First check if fallback name is a specific model (from car-specific actions)
        if fallback_name and is_specific_model_name(fallback_name):
            logger.info(f"🎯 Using specific model from action: {fallback_name}")
            return fallback_name

        # Then try to get from user car context
        car_context = get_user_car_context(user_id)
        if car_context and is_specific_model_name(car_context):
            logger.info(f"🎯 Using model from user context: {car_context}")
            return car_context

        # If no specific model found, return fallback or 'Not specified'
        result = fallback_name if fallback_name else 'Not specified'
        logger.info(f"🎯 No specific model found, using: {result}")
        return result

    except Exception as e:
        logger.error(f"❌ Error getting model name from context: {e}")
        return fallback_name or 'Not specified'

def handle_test_drive_callback_request(action_type, user_id, car_name=None):
    """
    Handle test drive and callback requests by saving user data to Google Sheet and Database

    Args:
        action_type (str): 'test_drive' or 'call_back'
        user_id (str): WhatsApp user ID (phone number)
        car_name (str): Name of the car (optional)

    Returns:
        bool: True if successfully saved, False otherwise
    """
    try:
        from sheet import store_test_drive_callback_data

        # Parse user ID for database operations
        actual_user_id, session_id, project_id = parse_user_id(user_id)

        # Get the most specific model name available
        model_name = get_model_name_from_context(user_id, car_name)

        # Prepare data for sheet
        request_data = {
            'user_id': user_id,
            'phone_number': user_id,  # WhatsApp user_id is the phone number
            'request_type': action_type,
            'car_name': model_name,
            'timestamp': datetime.now().isoformat(),
            'status': 'Pending'
        }

        # Store in Google Sheet
        sheet_success = store_test_drive_callback_data(request_data)

        # Store in Database
        db_success = False
        try:
            customer_data = {
                'name': 'Not provided',
                'phone': user_id,
                'email': 'Not provided',
                'car_model': model_name,
                'message': f'WhatsApp {action_type.replace("_", " ").title()} request'
            }

            booking_id = sync_insert_service_booking(
                actual_user_id,
                session_id,
                action_type,
                customer_data
            )

            if booking_id:
                logger.info(f"✅ {action_type.title()} request saved to database with ID {booking_id}")
                # Log the interaction
                sync_insert_user_interaction(
                    actual_user_id,
                    session_id,
                    f"{action_type}_request",
                    {"booking_id": booking_id, "car_name": model_name},
                    model_name
                )
                db_success = True
            else:
                logger.warning(f"⚠️ Failed to save {action_type} request to database")

        except Exception as db_error:
            logger.warning(f"Database booking failed: {db_error}")

        if sheet_success:
            logger.info(f"✅ {action_type.title()} request saved to Google Sheet for user {user_id}, model: {model_name}")
        else:
            logger.error(f"❌ Failed to save {action_type} request to Google Sheet for user {user_id}")

        # Return True if either storage method succeeded
        return sheet_success or db_success

    except Exception as e:
        logger.error(f"❌ Error handling {action_type} request: {e}")
        return False


def get_main_menu_message(action_name):
    """
    Get message from main_menu.json for specific actions

    Args:
        action_name (str): Name of the action (e.g., "Book a Test Drive", "Request a Callback")

    Returns:
        str: Message from main_menu.json or default message
    """
    try:
        # Load main menu data
        import json
        with open('main_menu.json', 'r', encoding='utf-8') as f:
            main_menu_data = json.load(f)

        # Get the message for the specific action
        if action_name in main_menu_data:
            action_data = main_menu_data[action_name]
            if isinstance(action_data, list) and len(action_data) > 0:
                return action_data[0].get('message', f"Thank you for your {action_name.lower()} request.")

        # Fallback messages
        if 'test' in action_name.lower() and 'drive' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for booking a test drive. Our dealer will contact you SOON."
        elif 'call_back' in action_name.lower() or 'call back' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for requesting a call back. Our dealer will contact you SOON."
        else:
            return f"Thank you for your {action_name.lower()} request."

    except Exception as e:
        logger.error(f"❌ Error loading main menu message for {action_name}: {e}")
        # Fallback messages
        if 'test' in action_name.lower() and 'drive' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for booking a test drive. Our dealer will contact you SOON."
        elif 'call_back' in action_name.lower() or 'call back' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for requesting a call back. Our dealer will contact you SOON."
        else:
            return f"Thank you for your {action_name.lower()} request."


def get_car_response_with_media_id(car_name, action_key=None, user_id=None):
    """Get car response with media_id support and handle special actions"""
    car_data = get_car_data_by_name(car_name)

    if not car_data:
        return None

    # Get the proper car name (handle name mappings)
    proper_car_name = car_name
    if car_name in CAR_DATA_REGISTRY:
        proper_car_name = car_name
    else:
        # Check if it's a mapped name
        normalized_name = car_name.lower().strip()
        if normalized_name in CAR_NAME_MAPPINGS:
            proper_car_name = CAR_NAME_MAPPINGS[normalized_name]

    # If action_key is provided, look for specific action
    if action_key and action_key in car_data:
        response_data = car_data[action_key]
    # Otherwise, look for the main car entry using proper name
    elif proper_car_name in car_data:
        response_data = car_data[proper_car_name]
    else:
        # Fallback: get first available entry
        first_key = next(iter(car_data.keys()), None)
        if first_key:
            response_data = car_data[first_key]
        else:
            return None

    if not response_data or not isinstance(response_data, list) or len(response_data) == 0:
        return None

    # Handle multi-item sections (Gallery, Main Menu, etc.) that have multiple items
    if len(response_data) > 1 and (action_key in ['Exterior', 'Interior', 'Gallery'] or proper_car_name == 'Main Menu'):
        # For multi-item sections, combine the data from both items
        first_item = response_data[0]
        second_item = response_data[1] if len(response_data) > 1 else {}

        # Get data from first item
        first_message = first_item.get("message", "")
        first_data = first_item.get("data", [])
        first_data_type = first_item.get("data_type", "button")
        media_id = first_item.get("Media_ID", "")

        # Get data from second item
        second_message = second_item.get("message", "")
        second_data = second_item.get("data", [])
        second_data_type = second_item.get("data_type", "list")

        # For Main Menu, combine messages properly
        if proper_car_name == 'Main Menu':
            combined_message = f"{first_message}\n\n{second_message}"
        else:
            # For gallery sections (Exterior, Interior), avoid duplicating the "click below" message
            # Only use the first message to avoid duplication
            combined_message = first_message

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": combined_message,
            "data": first_data,
            "data_type": first_data_type,
            "buttons": [{
                "data": first_data,
                "data_type": first_data_type,
                "message": first_message
            }, {
                "data": second_data,
                "data_type": second_data_type,
                "message": second_message
            }],
            "hasButtons": len(second_data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    else:
        # Handle regular sections (single item)
        step_config = response_data[0]
        message = step_config.get("message", "")
        data = step_config.get("data", [])
        data_type = step_config.get("data_type", "list")
        media_id = step_config.get("Media_ID", "")  # Extract Media_ID if present

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": message,
            "data": data,
            "data_type": data_type,
            "buttons": [{
                "data": data,
                "data_type": data_type,
                "message": message
            }],
            "hasButtons": len(data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    # Update user's car context when viewing a car
    if proper_car_name and user_id and proper_car_name != 'Not specified':
        update_user_car_context(user_id, proper_car_name)

    # Handle special actions: Book Test Drive and Request a Call Back
    if action_key and user_id:
        action_lower = action_key.lower()
        if 'test' in action_lower and 'drive' in action_lower:
            # Handle Book Test Drive
            handle_test_drive_callback_request('test_drive', user_id, proper_car_name)
            # Use message from main_menu.json
            response["message"] = get_main_menu_message("Book a Test Drive")

        elif 'call' in action_lower and 'back' in action_lower:
            # Handle Request a Call Back
            handle_test_drive_callback_request('call_back', user_id, proper_car_name)
            # Use message from main_menu.json
            response["message"] = get_main_menu_message("Request a Call Back")

    return response
base_system_prompt = load_system_prompt()
# Load knowledge base and information
knowledge_base_content = load_information_file()

# Create enhanced system prompt with knowledge base
system_prompt = create_enhanced_system_prompt(base_system_prompt, knowledge_base_content)
load_dotenv()
app = Flask(__name__)
app.config["DEBUG"] = False
CORS(app)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')


# Initialize Gemini AI
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

model = genai.GenerativeModel(
    model_name="models/gemini-2.0-flash",
    tools=TOOL_DEF,
    system_instruction=system_prompt
) if GEMINI_API_KEY else None
SESSION_ID = {}
# Store user context for each session
USER_CONTEXT = {}

# Simple rate limiting for AI calls
last_ai_call_time = 0
AI_CALL_INTERVAL = 2  # Minimum seconds between AI calls

def search_knowledge_base(query):
    """Search knowledge base for specific information - simplified approach"""
    if not knowledge_base_content:
        return None

    query_lower = query.lower()
    lines = knowledge_base_content.split('\n')

    # Look for relevant Q&A pairs with scoring for better matching
    best_match = None
    best_score = 0

    for i, line in enumerate(lines):
        if line.startswith('Q:'):
            line_lower = line.lower()

            # Calculate relevance score
            score = 0
            query_words = query_lower.split()

            # Give higher score for exact matches of important keywords
            if 'arena' in query_lower and 'arena' in line_lower:
                score += 10
            elif 'nexa' in query_lower and 'nexa' in line_lower:
                score += 10

            # Score for other matching words
            for word in query_words:
                if len(word) > 2 and word in line_lower:  # Skip short words like "of", "is"
                    score += 1

            # If this is a better match, store it
            if score > best_score and score > 2:  # Minimum threshold
                if i + 1 < len(lines) and lines[i + 1].startswith('A:'):
                    question = line[2:].strip()  # Remove 'Q:'
                    answer = lines[i + 1][2:].strip()  # Remove 'A:'

                    # Get additional lines that are part of the answer
                    full_answer = answer
                    j = i + 2
                    while j < len(lines) and not lines[j].startswith('Q:') and lines[j].strip():
                        full_answer += '\n' + lines[j].strip()
                        j += 1

                    best_match = {
                        "status": "success",
                        "message": f"📍 **{question}**\n\n{full_answer}",
                        "source": "knowledge_base"
                    }
                    best_score = score

    return best_match

# Removed complex mapping functions - let LLM handle natural language understanding

def get_arena_and_nexa_cars():
    """Get properly categorized Arena and NEXA cars from registry"""

    # Hardcoded correct categorization based on actual Maruti Suzuki lineup
    arena_cars = ["Alto K10", "Swift", "Dzire", "Brezza", "Celerio", "Eeco", "Ertiga", "S-Presso", "Wagon-R"]
    nexa_cars = ["Baleno", "Fronx", "Grand Vitara", "Ignis", "Ciaz", "Jimny", "XL6"]

    # Filter to only include cars that exist in our registry
    available_arena = []
    available_nexa = []

    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name == 'Main Menu':
            continue
        if car_name in arena_cars:
            available_arena.append(car_name)
        elif car_name in nexa_cars:
            available_nexa.append(car_name)

    return available_arena, available_nexa

def parse_weight_requirement(prompt_lower):
    """Parse weight requirement from prompt (kg or tons) and return weight in kg (int) or None"""

    try:
        import re as _re
        # Examples: 10500kg, 10.5 ton, 11 tons, 10 tonne
        m = _re.search(r"(\d+(?:\.\d+)?)\s*(kg|kgs|kilogram|kilograms|ton|tons|tonne|tonnes|t)", prompt_lower)
        if not m:
            return None
        value = float(m.group(1))
        unit = m.group(2)
        if unit.startswith('kg') or unit.startswith('kilogram'):
            return int(round(value))
        # convert tons to kg (metric tons)
        return int(round(value * 1000))
    except Exception:
        return None

def search_vehicles_by_capacity(required_capacity_kg, tolerance_percent=20):
    """
    Search for vehicles (trucks, vans, buses) by payload capacity

    Args:
        required_capacity_kg (int): Required capacity in kg
        tolerance_percent (int): Tolerance percentage for capacity matching

    Returns:
        list: List of vehicles that meet or exceed the capacity requirement
    """
    try:
        # Import vehicle data
        from database.truck import trucks_data
        from database.small_trucks import small_trucks_data

        # Combine all vehicle data
        all_vehicles_data = {**trucks_data, **small_trucks_data}

        matching_vehicles = []

        for vehicle_key, vehicle_data in all_vehicles_data.items():
            vehicle_name = vehicle_data.get('name', '')
            specifications = vehicle_data.get('specifications', {})

            # Get GVW (Gross Vehicle Weight) - this includes payload capacity
            gvw_str = specifications.get('gvw', '')
            payload_str = specifications.get('payload', '')

            # Extract numeric value from GVW or payload
            import re
            capacity_kg = 0

            # First try to get payload if available
            if payload_str:
                payload_match = re.search(r'(\d+)', str(payload_str))
                if payload_match:
                    capacity_kg = int(payload_match.group(1))

            # If no payload, estimate from GVW (typically payload is 60-70% of GVW for trucks)
            elif gvw_str:
                gvw_match = re.search(r'(\d+)', str(gvw_str))
                if gvw_match:
                    gvw_kg = int(gvw_match.group(1))
                    # Estimate payload as 65% of GVW for trucks
                    capacity_kg = int(gvw_kg * 0.65)

            # Check if vehicle meets capacity requirement (with tolerance)
            min_acceptable = required_capacity_kg * (1 - tolerance_percent/100)
            max_acceptable = required_capacity_kg * (1 + tolerance_percent/100)

            if capacity_kg >= min_acceptable:
                # Add vehicle with capacity info
                vehicle_info = {
                    'name': vehicle_name,
                    'category': vehicle_data.get('category', ''),
                    'estimated_payload_kg': capacity_kg,
                    'gvw': gvw_str,
                    'actual_payload': payload_str,
                    'fuel_types': vehicle_data.get('fuel_types', ['Diesel']),
                    'specifications': specifications,
                    'vehicle_data': vehicle_data
                }
                matching_vehicles.append(vehicle_info)

        # Sort by capacity (closest to requirement first, then higher capacity)
        def capacity_score(vehicle):
            capacity = vehicle['estimated_payload_kg']
            if capacity >= required_capacity_kg:
                # Prefer vehicles close to requirement
                return abs(capacity - required_capacity_kg)
            else:
                # Penalize vehicles below requirement
                return required_capacity_kg * 2

        matching_vehicles.sort(key=capacity_score)

        return matching_vehicles[:15]  # Return top 15 matches

    except Exception as e:
        logger.error(f"Error searching vehicles by capacity: {e}")
        return []

def handle_category_queries(prompt_lower, user_id=None):
    """Handle category-based queries like 'arena cars', 'nexa cars', etc."""

    arena_cars, nexa_cars = get_arena_and_nexa_cars()

    if any(phrase in prompt_lower for phrase in ["arena cars", "arena models", "maruti suzuki arena"]):
        return {
            "message": f"🏟️ **Arena Cars - Trusted & Reliable**\n\nOur Arena lineup offers dependable, value-for-money vehicles:\n\n" +
                      "\n".join([f"🚗 {car}" for car in arena_cars[:9]]) +
                      f"\n\n📋 Total: {len(arena_cars)} models available\n\n*Which car interests you?*",
            "buttons": [{"data": arena_cars[:9], "data_type": "list", "message": "Select an Arena car:"}],
            "function_response": []
        }

    elif any(phrase in prompt_lower for phrase in ["nexa cars", "nexa models", "premium cars"]):
        return {
            "message": f"🌟 **NEXA Cars - Premium & Stylish**\n\nOur NEXA collection features premium vehicles with advanced features:\n\n" +
                      "\n".join([f"✨ {car}" for car in nexa_cars]) +
                      f"\n\n📋 Total: {len(nexa_cars)} models available\n\n*Which premium car would you like to explore?*",
            "buttons": [{"data": nexa_cars, "data_type": "list", "message": "Select a NEXA car:"}],
            "function_response": []
        }

    elif any(phrase in prompt_lower for phrase in ["all cars", "show me all", "complete car list", "all models"]):
        total_cars = len(arena_cars) + len(nexa_cars)
        return {
            "message": f"🚗 **Complete Maruti Suzuki Range**\n\n🏟️ **Arena Cars** ({len(arena_cars)} models):\n{', '.join(arena_cars[:4])}...\n\n🌟 **NEXA Cars** ({len(nexa_cars)} models):\n{', '.join(nexa_cars[:4])}...\n\n📋 **Total Portfolio**: {total_cars} models\n\n*Choose a category or specific car:*",
            "buttons": [{"data": ["Arena Cars", "NEXA Cars"] + arena_cars[:3] + nexa_cars[:3], "data_type": "list", "message": "Explore our range:"}],
            "function_response": []
        }

    return None

def handle_feature_queries(prompt_lower, user_id=None):
    """Handle feature-specific queries like 'swift features', 'baleno safety', etc."""

    # Feature mappings
    feature_mappings = {
        "features": "alto_k10 features",
        "safety": "More about this",
        "interior": "More about this",
        "technology": "More about this",
        "specifications": "More about this",
        "details": "More about this"
    }

    # Check if it's a car + feature query
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
            # Check for feature keywords
            for feature_key, action in feature_mappings.items():
                if feature_key in prompt_lower:
                    # Try to get the car's "More about this" action
                    car_data = get_car_data_by_name(car_name)
                    if car_data and action in car_data:
                        logger.info(f"✅ Feature query match: '{prompt_lower}' -> {car_name} -> {action}")
                        return get_car_response_with_media_id(car_name, action)
            break

    return None

def check_whatsapp_flow_match(prompt, user_id=None):
    """
    Check if the prompt matches any WhatsApp flow step using NEW CAR DATA SYSTEM
    Prioritizes exact matches and car-specific actions

    Args:
        prompt (str): User input prompt
        user_id (str): User ID for context lookup

    Returns:
        dict or None: WhatsApp response if match found, None otherwise
    """
    prompt_clean = prompt.strip()
    prompt_lower = prompt_clean.lower()

    # PRIORITY 0: Check for exact WhatsApp flow button matches FIRST
    # This ensures button clicks always go to WhatsApp flows, not LLM
    if prompt_clean in MAIN_MENU_DATA:
        logger.info(f"✅ PRIORITY WhatsApp flow button match: '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # Case-insensitive main menu matches
    for key in MAIN_MENU_DATA.keys():
        if key.lower() == prompt_clean.lower():
            logger.info(f"✅ PRIORITY WhatsApp flow button match (case-insensitive): '{prompt}' -> {key}")
            return get_whatsapp_response(key, user_id)

    # PRIORITY 0.05: Handle common button text variations and mismatches
    # Map common variations to correct main menu keys
    button_variations = {
        'Ace CNG/Bi-Fuel Mode': 'Ace CNG/Bi-Fuel Models',
        'Ace CNG & Bi-Fuel': 'Ace CNG/Bi-Fuel Models',
        'Ace EV Mode': 'Ace EV Models',
        'Ace Petrol Mode': 'Ace Petrol Models',
        'Ace Diesel Mode': 'Ace Diesel Models',
        'Intra EV Mode': 'Intra EV Models',
        'Intra Petrol Mode': 'Intra Petrol Models',
        'Intra Diesel Mode': 'Intra Diesel Models',
        'Yodha EV Mode': 'Yodha EV Models',
        'Yodha Petrol Mode': 'Yodha Petrol Models',
        'Yodha Diesel Mode': 'Yodha Diesel Models'
    }

    if prompt_clean in button_variations:
        correct_key = button_variations[prompt_clean]
        if correct_key in MAIN_MENU_DATA:
            logger.info(f"✅ PRIORITY WhatsApp flow button match (variation): '{prompt}' -> {correct_key}")
            return get_whatsapp_response(correct_key, user_id)

    # PRIORITY 0.1: Direct car name match (high priority for button clicks)
    car_response = get_car_response_with_media_id(prompt_clean, user_id=user_id)
    if car_response:
        proper_car_name = car_response.get('car_name', prompt_clean)
        logger.info(f"✅ PRIORITY Direct car match: '{prompt}' -> {proper_car_name}")
        # Set user context for direct car matches using proper car name
        if user_id:
            set_user_context(user_id, proper_car_name)
            logger.info(f"🎯 Set user context: {user_id} -> {proper_car_name}")
        return car_response

    # PRIORITY 0.2: Handle generic "car" queries - don't let them match "Tata Commercials"
    # These should go to the AI functions instead
    generic_car_queries = [
        'car', 'cars', 'show me cars', 'i want cars', 'looking for cars',
        'i want to buy a car', 'show cars', 'all cars', 'available cars',
        'what cars do you have', 'car models', 'car options'
    ]
    if prompt_lower.strip() in generic_car_queries:
        logger.info(f"🚗 Generic car query detected: '{prompt}' - routing to AI functions")
        return None  # Let AI functions handle this

    # PRIORITY 0.5: Handle van/bus queries - map to menu system with explanations
    # BUT EXCLUDE exact menu button matches to avoid interference

    # PRIORITY 0.3: Handle service-related queries - route to WhatsApp flows
    def matches_service_query(prompt):
        service_patterns = [
            'i want to book a service', 'i need a service', 'book a service',
            'service booking', 'book service', 'schedule service', 'service appointment',
            'car service', 'vehicle service', 'maintenance', 'repair',
            'i want service', 'need service', 'book my service', 'i want to service',
            'need to service', 'want to book service'
        ]
        # Check for exact matches first, then partial matches
        prompt_lower = prompt.lower().strip()

        # Direct matches
        if prompt_lower in service_patterns:
            return True

        # Partial matches for longer phrases
        for pattern in service_patterns:
            if pattern in prompt_lower and len(prompt_lower) <= len(pattern) + 10:
                return True

        return False

    def matches_test_drive_query(prompt):
        test_drive_patterns = [
            'i want to book a test drive', 'book a test drive', 'test drive',
            'i want test drive', 'book test drive', 'schedule test drive',
            'i want to test drive', 'can i test drive', 'test drive booking'
        ]
        return any(pattern in prompt.lower() for pattern in test_drive_patterns)

    def matches_call_back_query(prompt):
        call_back_patterns = [
            'request a call back', 'call back', 'call me back', 'callback',
            'i want a call back', 'request call back', 'please call me',
            'can you call me', 'i need a call back'
        ]
        return any(pattern in prompt.lower() for pattern in call_back_patterns)

    # Check for service-related queries first
    if matches_service_query(prompt_clean):
        logger.info(f"🔧 Service query detected: '{prompt}' - routing to Book a Service flow")
        base_response = get_whatsapp_response("Book a Service", user_id)
        if base_response:
            return base_response
        return None

    if matches_test_drive_query(prompt_clean):
        logger.info(f"🚗 Test drive query detected: '{prompt}' - routing to Book a Test Drive flow")
        base_response = get_whatsapp_response("Book a Test Drive", user_id)
        if base_response:
            return base_response
        return None

    if matches_call_back_query(prompt_clean):
        logger.info(f"📞 Call back query detected: '{prompt}' - routing to Request a Call Back flow")
        base_response = get_whatsapp_response("Request a Call Back", user_id)
        if base_response:
            return base_response
        return None

    # Check if this is an exact menu button match first
    exact_menu_buttons = [
        'vans & buses', 'small trucks', 'trucks', 'tata commercials',
        'school vans', 'tourist vans', 'ambulance vans', 'buses',
        'school buses', 'staff contract', 'book a service', 'book a test drive',
        'request a call back'
    ]

    if prompt_lower.strip() in exact_menu_buttons:
        logger.info(f"📋 Exact menu button detected: '{prompt}' - using standard menu flow")
        # Let the standard menu flow handle this
        pass  # Continue to standard menu matching below
    else:
        # Enhanced patterns to handle natural language variations
        def matches_van_query(prompt):
            van_patterns = [
                'i want to buy a van', 'i need a van', 'show me vans',
                'i want a van', 'looking for van', 'looking for vans', 'what vans do you have',
                'van for', 'vans for', 'need van for', 'want van for', 'buy van for',
                'school van', 'tourist van', 'ambulance van', 'passenger van',
                'cargo van', 'delivery van', 'transport van'
            ]
            # Only match if it's a natural language query, not just "van" or "vans"
            return any(pattern in prompt.lower() for pattern in van_patterns)

        def matches_bus_query(prompt):
            bus_patterns = [
                'i want to buy a bus', 'i need a bus', 'show me buses',
                'i want a bus', 'looking for bus', 'looking for buses', 'what buses do you have',
                'bus for', 'buses for', 'need bus for', 'want bus for', 'buy bus for',
                'school bus', 'student bus', 'staff bus', 'employee bus', 'contract bus',
                'passenger bus', 'transport bus', 'bus for students', 'bus for staff',
                'bus for employees', 'bus for school', 'bus for transport'
            ]
            # Only match if it's a natural language query, not just "bus" or "buses"
            return any(pattern in prompt.lower() for pattern in bus_patterns)

        if matches_van_query(prompt_clean):
            logger.info(f"🚐 Natural language van query detected: '{prompt}' - routing to Vans menu with explanation")
            # Get the base response from menu
            base_response = get_whatsapp_response("Vans", user_id)
            if base_response:
                # Replace the original message with enhanced explanation + menu text
                enhanced_message = "🚐 **Vans for all your transport needs!**\n\nVans are excellent choices for passenger transport, cargo delivery, or specialized services. At Bhandari Automobiles, we offer a comprehensive range of vans to suit your specific needs:\n\n• **School Vans**: Designed for safe and reliable student transportation with proper safety features\n• **Tourist Vans**: Comfortable and spacious for travel businesses and tourism\n• **Ambulance Vans**: Specialized vehicles equipped for medical emergencies and patient care\n\nEach category is designed with specific safety features and configurations to meet your requirements.\n\n*Types of Vans*\nSelect a category to Explore:"

                # Replace the message completely to avoid duplication
                base_response["message"] = enhanced_message

                return base_response
            return None

        if matches_bus_query(prompt_clean):
            logger.info(f"🚌 Natural language bus query detected: '{prompt}' - routing to Buses menu with explanation")
            # Get the base response from menu
            base_response = get_whatsapp_response("Buses", user_id)
            if base_response:
                # Replace the original message with enhanced explanation + menu text
                enhanced_message = "🚌 **Buses for passenger transport!**\n\nBuses are reliable and efficient vehicles designed for various passenger transport needs. At Bhandari Automobiles, we offer different types of buses to serve your specific requirements:\n\n• **School Buses**: Safe and reliable buses designed specifically for student transportation\n• **Staff & Contract Buses**: Comfortable buses perfect for employee transport and contract services\n\nOur buses are built with safety, comfort, and reliability in mind to ensure smooth passenger transport operations.\n\n*Tata Buses*\nSelect a Bus model to Explore:"

                # Replace the message completely to avoid duplication
                base_response["message"] = enhanced_message

                return base_response
            return None





    # PRIORITY 3: Check for car actions using new system - CONTEXT FIRST
    # First, check if user has a current car context and the action exists in that car
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            car_data = get_car_data_by_name(current_car)

            # Try exact match first
            if car_data and prompt_clean in car_data:
                response = get_car_response_with_media_id(current_car, prompt_clean, user_id)
                if response:
                    logger.info(f"✅ Context-based car action match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return response

            # Try smart action mapping for common actions
            if car_data:
                action_mappings = {
                    'Ex-showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    '🚗Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex-Showroom Prices': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'More about this car': ['More about this', 'More about this car'],
                    'Request Brochure': ['Request Brochure', 'Request for Brochure', 'Brochure'],
                    'Book Test Drive': ['Book Test Drive', 'Book a Test Drive']
                }

                if prompt_clean in action_mappings:
                    for mapped_action in action_mappings[prompt_clean]:
                        if mapped_action in car_data:
                            response = get_car_response_with_media_id(current_car, mapped_action, user_id)
                            if response:
                                logger.info(f"✅ Context-based smart action match: '{prompt}' -> {current_car} -> {mapped_action}")
                                return response

    # If no context or context didn't match, check all cars
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name == 'Main Menu':
            continue

        car_data = CAR_DATA_REGISTRY[car_name]

        # Check if prompt matches any action in this car's data
        if prompt_clean in car_data:
            # Use get_car_response_with_media_id with specific car and action
            response = get_car_response_with_media_id(car_name, prompt_clean, user_id)
            if response:
                logger.info(f"✅ Car action match: '{prompt}' -> {car_name} -> {prompt_clean}")
                # Set user context
                if user_id:
                    set_user_context(user_id, car_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                return response

    # PRIORITY 2.5: Handle category queries (Arena, NEXA, All Cars)
    category_result = handle_category_queries(prompt_lower, user_id)
    if category_result:
        logger.info(f"✅ Category query match: '{prompt}'")
        return category_result

    # PRIORITY 2.6: Handle feature queries (car + feature combinations)
    feature_result = handle_feature_queries(prompt_lower, user_id)
    if feature_result:
        return feature_result

    # PRIORITY 2.7: Handle budget queries using Universal Budget Handler - works for ALL vehicle types
    # Check for explicit budget keywords
    if any(phrase in prompt_lower for phrase in ['under', 'from', 'budget', 'starting', 'below', 'above', 'between', 'within', 'maximum', 'minimum']):
        if any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l']):
            budget_result = handle_universal_budget_query(prompt, user_id)
            if budget_result:
                logger.info(f"✅ Universal budget query match: '{prompt}'")
                return budget_result

    # Check for implicit budget patterns like "i have 10l i want suv"
    elif any(phrase in prompt_lower for phrase in ['i have', 'my budget is', 'budget of']):
        if any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l']) and any(vehicle_word in prompt_lower for vehicle_word in ['car', 'suv', 'truck', 'van', 'bus', 'vehicle']):
            budget_result = handle_universal_budget_query(prompt, user_id)
            if budget_result:
                logger.info(f"✅ Implicit budget query match: '{prompt}'")
                return budget_result

    # PRIORITY 3: Handle comparison requests using universal comparison system
    if any(phrase in prompt_lower for phrase in ['both variants', 'both prices', 'both variant', 'show me both', 'variants of both', 'compare', 'vs', 'versus', 'difference between', 'diffrence between']):
        logger.info(f"🔍 Universal comparison request detected: '{prompt}'")

        # Use the universal comparison function to extract vehicles
        comparison_buttons = generate_universal_comparison_buttons(prompt)

        if comparison_buttons and len(comparison_buttons[0]['data']) >= 2:
            # Get the first two vehicles for comparison
            vehicles = comparison_buttons[0]['data']
            vehicle1, vehicle2 = vehicles[0], vehicles[1]
            logger.info(f"🔍 Comparing: {vehicle1} vs {vehicle2}")

            # Use the unified comparison function
            comparison_result = compare_vehicles_unified(vehicle1, vehicle2)

            if comparison_result:
                return {
                    "message": comparison_result,
                    "buttons": [],
                    "function_response": [{
                        "data": vehicles[:2],  # Show both compared vehicles
                        "data_type": "list",
                        "message": "📋 Select a vehicle for detailed specifications:"

                    }],
                    "function_response_id": 1
                }

        # Fallback for comparison requests
        return {
            "message": "🔍 **Car Comparison**\n\nPlease specify which cars you'd like to compare. For example:\n• 'Compare Swift vs Brezza'\n• 'Dzire vs Baleno variants'\n• 'Show me both Swift and Fronx'",
            "data": [],
            "data_type": "text",
            "buttons": []
        }

    # PRIORITY 4: Check for exact matches in WHATSAPP_CONFIG
    if prompt_clean in WHATSAPP_CONFIG:
        logger.info(f"✅ WhatsApp config match: '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # PRIORITY 5: Context-based actions using new system (only if no specific car mentioned)
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            # Check if a different car is mentioned in the prompt
            different_car_mentioned = False
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name != current_car and car_name.lower() in prompt_lower:
                    different_car_mentioned = True
                    break

            # Also check name mappings for different cars
            if not different_car_mentioned:
                for mapping, car_name in CAR_NAME_MAPPINGS.items():
                    if car_name != current_car and mapping in prompt_lower:
                        different_car_mentioned = True
                        break

            # Only use context if no different car is mentioned
            if not different_car_mentioned:
                # Try to find action in current car's data
                car_data = get_car_data_by_name(current_car)
                if car_data and prompt_clean in car_data:
                    logger.info(f"✅ Context-based match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return get_whatsapp_response(prompt_clean, user_id)

                # Try common action patterns with smart mapping
                if any(word in prompt_lower for word in ['price', 'variant', 'ex-showroom', 'showroom']):
                    # Use unified price key detection
                    price_key = get_price_key_for_car(car_data)
                    if price_key:
                        logger.info(f"✅ Context-based price match: '{prompt}' -> {current_car} -> {price_key}")
                        return get_whatsapp_response(price_key, user_id)

    # PRIORITY 6: Smart pattern matching using new system
    # Check for car-specific patterns like "more about", "price", etc.
    # Order matters - more specific patterns first!
    # Handle variant/price requests with unified approach
    variant_patterns = ['variants', 'variant', 'variantes', 'price', 'prices', 'ex-showroom', 'showroom']
    if any(pattern in prompt_lower for pattern in variant_patterns):
        logger.info(f"🔍 Variant/price request detected: '{prompt}'")

        # Find which car is mentioned
        detected_car = None

        # Check direct car names first
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                detected_car = car_name
                break

        # Check name mappings if no direct match
        if not detected_car:
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    detected_car = car_name
                    break

        # If we found a car, try to get its variants/prices
        if detected_car:
            logger.info(f"🚗 Detected car for variant request: {detected_car}")
            car_data = get_car_data_by_name(detected_car)

            if car_data:
                # Use unified price key detection
                price_key = get_price_key_for_car(car_data)
                if price_key:
                    logger.info(f"✅ Found price key for {detected_car}: {price_key}")
                    response = get_car_response_with_media_id(detected_car, price_key, user_id)
                    if response:
                        # Set user context
                        if user_id:
                            set_user_context(user_id, detected_car)
                            logger.info(f"🎯 Set user context: {user_id} -> {detected_car}")
                        return response

    # Handle other patterns
    other_patterns = {
        'more about': ['More about', 'More about this'],
        'tell me about': ['More about', 'More about this'],
        'show me': ['More about', 'More about this'],
        'details of': ['More about', 'More about this'],
        'information about': ['More about', 'More about this'],
        'brochure': ['Brochure', 'Request Brochure', 'Request for Brochure'],
        'test drive': ['Book Test Drive', 'Book a Test Drive']
    }

    for pattern, action_variations in other_patterns.items():
        if pattern in prompt_lower:
            # Find which car is mentioned
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"{action_suffix} {car_name}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action, user_id)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

            # Also check name mappings
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Mapping pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action, user_id)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

    # PRIORITY 7: Check if this should go to LLM (information queries)
    info_keywords = [
        'information', 'info', 'details', 'detail', 'address', 'contact',
        'phone', 'number', 'location', 'where', 'how to', 'tell me about',
        'dealer', 'dealership', 'showroom address', 'showroom contact',
        'working hours', 'timings', 'directions', 'reach'
    ]

    if any(keyword in prompt_lower for keyword in info_keywords):
        logger.info(f"🤖 Routing to LLM: '{prompt}' (detected info query)")
        return None

    return None

def get_whatsapp_response(step_name, user_id=None):
    """
    Get response using new car data system with media_id support - NO NAMESPACING

    Args:
        step_name (str): The step name to get response for (car name or action)
        user_id (str): User ID for context setting

    Returns:
        dict: Formatted response with message, data, and media_id if available
    """
    try:
        # Handle special main menu actions that require user_id capture
        if step_name in ["Request a Call Back", "Book a Test Drive", "Talk With Live Agent", "Chat With Bot"] and user_id:
            # Get user's car context (last viewed car) or fallback to 'Not specified'
            car_context = get_user_car_context(user_id)

            # Save user_id to appropriate Google Sheet
            if step_name == "Request a Call Back":
                handle_test_drive_callback_request('call_back', user_id, car_context)
                logger.info(f"✅ Call back request saved for user {user_id} from main menu")
            elif step_name == "Book a Test Drive":
                handle_test_drive_callback_request('test_drive', user_id, car_context)
                logger.info(f"✅ Test drive request saved for user {user_id} from main menu")
            elif step_name == "Talk With Live Agent":
                # Parse user ID for database operations
                actual_user_id, session_id_parsed, project_id = parse_user_id(user_id)
                # Create live chat session in database
                session_token = sync_create_live_chat_session(actual_user_id, session_id_parsed)
                if session_token:
                    logger.info(f"✅ Live chat session created for user {user_id}: {session_token}")
                    # Log the interaction
                    sync_insert_user_interaction(actual_user_id, session_id_parsed, "live_chat_request",
                                                {"session_token": session_token, "car_context": car_context})
                else:
                    logger.warning(f"❌ Failed to create live chat session for user {user_id}")
            elif step_name == "Chat With Bot":
                # Parse user ID for database operations
                actual_user_id, session_id_parsed, project_id = parse_user_id(user_id)
                # Log bot chat interaction
                sync_insert_user_interaction(actual_user_id, session_id_parsed, "bot_chat_request",
                                           {"car_context": car_context})
                logger.info(f"✅ Bot chat interaction logged for user {user_id}")

        # First, check if it's a main menu entry (prioritize main menu over car data)
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle main menu entries
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for entry in response_data:
                        if "message" in entry:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += entry["message"]

                        if "data" in entry:
                            combined_buttons.append({
                                "data": entry["data"],
                                "data_type": entry.get("data_type", "list"),
                                "message": entry.get("message", "")
                            })

                        if "Media_ID" in entry and not media_id:
                            media_id = entry["Media_ID"]

                        # Use the data_type from the first entry that has it
                        if "data_type" in entry:
                            primary_data_type = entry["data_type"]

                    # Fix for WhatsApp: Set the first button's message to the combined message
                    # to avoid duplication when WhatsApp reads the button message
                    if combined_buttons:
                        combined_buttons[0]["message"] = combined_message

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "text")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }]
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response

        # Then, try to get car data directly (no namespacing) - but skip Main Menu
        if step_name != "Main Menu":
            car_response = get_car_response_with_media_id(step_name, user_id=user_id)
            if car_response:
                # Set user context for car-specific responses
                if user_id:
                    set_user_context(user_id, step_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {step_name}")
                return car_response

        # Check if it's a car action - but prioritize user context if available
        current_car = None
        if user_id:
            current_car = get_user_context(user_id)

        # If user has a current car context, check that car first
        if current_car:
            car_data = get_car_data_by_name(current_car)
            if car_data and step_name in car_data:
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    # Handle multi-item sections (Exterior, Interior, Gallery)
                    if len(response_data) > 1 and step_name in ['Exterior', 'Interior', 'Gallery']:
                        # For gallery sections, avoid duplicating the "click below" message
                        first_item = response_data[0]
                        second_item = response_data[1] if len(response_data) > 1 else {}

                        # Get data from first item (with images)
                        first_message = first_item.get("message", "")
                        first_data = first_item.get("data", [])
                        first_data_type = first_item.get("data_type", "image")
                        media_id = first_item.get("Media_ID", "")

                        # Get data from second item (with buttons)
                        second_data = second_item.get("data", [])
                        second_data_type = second_item.get("data_type", "button")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": current_car,
                            "message": first_message,
                            "data": first_data,
                            "data_type": first_data_type,
                            "buttons": [{
                                "data": first_data,
                                "data_type": first_data_type,
                                "message": first_message
                            }, {
                                "data": second_data,
                                "data_type": second_data_type,
                                "message": "👇For more options click below 🔗✨"
                            }],
                            "hasButtons": len(second_data) > 0
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id
                    else:
                        # Handle single item or non-gallery sections
                        step_config = response_data[0]
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        media_id = step_config.get("Media_ID", "")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": current_car,
                            "message": message,
                            "buttons": [{
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            }],
                            "hasButtons": len(data) > 0,
                            "data_type": data_type
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id

                    logger.info(f"✅ Context-based action match: {current_car} -> {step_name}")
                    return response

        # If no context or context didn't match, check all cars (but this should be rare now)
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name == 'Main Menu':
                continue

            car_data = CAR_DATA_REGISTRY[car_name]
            if step_name in car_data:
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    # Handle multi-item sections (Exterior, Interior, Gallery)
                    if len(response_data) > 1 and step_name in ['Exterior', 'Interior', 'Gallery']:
                        # For gallery sections, avoid duplicating the "click below" message
                        first_item = response_data[0]
                        second_item = response_data[1] if len(response_data) > 1 else {}

                        # Get data from first item (with images)
                        first_message = first_item.get("message", "")
                        first_data = first_item.get("data", [])
                        first_data_type = first_item.get("data_type", "image")
                        media_id = first_item.get("Media_ID", "")

                        # Get data from second item (with buttons)
                        second_data = second_item.get("data", [])
                        second_data_type = second_item.get("data_type", "button")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": car_name,
                            "message": first_message,
                            "data": first_data,
                            "data_type": first_data_type,
                            "buttons": [{
                                "data": first_data,
                                "data_type": first_data_type,
                                "message": first_message
                            }, {
                                "data": second_data,
                                "data_type": second_data_type,
                                "message": "👇For more options click below 🔗✨"
                            }],
                            "hasButtons": len(second_data) > 0
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id
                    else:
                        # Handle single item or non-gallery sections
                        step_config = response_data[0]
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        media_id = step_config.get("Media_ID", "")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": car_name,
                            "message": message,
                            "buttons": [{
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            }],
                            "hasButtons": len(data) > 0,
                            "data_type": data_type
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id

                    # Set user context
                    if user_id:
                        set_user_context(user_id, car_name)
                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")

                    logger.info(f"✅ General action match: {car_name} -> {step_name}")
                    return response

        # Fallback to main menu or WHATSAPP_CONFIG
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle multiple entries in main menu
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for i, step_config in enumerate(response_data):
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        config_media_id = step_config.get("Media_ID", "")

                        # Combine messages with line breaks
                        if message:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += message

                        # Create separate button entries for each step
                        if data:
                            combined_buttons.append({
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            })

                            # Add media_id if present
                            if config_media_id:
                                combined_buttons[-1]["media_id"] = config_media_id
                                if not media_id:  # Use first media_id found
                                    media_id = config_media_id

                        # Use the first data_type as primary
                        if i == 0:
                            primary_data_type = data_type

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons,
                        "hasButtons": len(combined_buttons) > 0,
                        "data_type": primary_data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry - original logic
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }],
                        "hasButtons": len(data) > 0,
                        "data_type": data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id
                        response["buttons"][0]["media_id"] = media_id

                    return response

        # Final fallback to old WHATSAPP_CONFIG (for backward compatibility)
        if step_name in WHATSAPP_CONFIG:
            step_data = WHATSAPP_CONFIG[step_name]
            if isinstance(step_data, list) and len(step_data) > 0:
                step_config = step_data[0]
                message = step_config.get("message", "")
                data = step_config.get("data", [])
                data_type = step_config.get("data_type", "list")
                media_id = step_config.get("Media_ID", "")

                response = {
                    "status": "success",
                    "step": step_name,
                    "message": message,
                    "buttons": [{
                        "data": data,
                        "data_type": data_type,
                        "message": message
                    }],
                    "hasButtons": len(data) > 0,
                    "data_type": data_type
                }

                # Add media_id if present
                if media_id:
                    response["media_id"] = media_id
                    response["buttons"][0]["media_id"] = media_id

                return response

        # Not found
        return {
            "status": "error",
            "message": f"Step '{step_name}' not found in any configuration",
            "data": None,
            "data_type": "text"
        }

    except Exception as e:
        logger.error(f"Error in get_whatsapp_response: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting WhatsApp response: {str(e)}"
        }

def set_user_context(session_id, car_name):
    """Set the current car context for a user session"""
    if session_id:
        USER_CONTEXT[session_id] = {
            'current_car': car_name,
            'timestamp': time.time()
        }

def get_user_context(session_id):
    """Get the current car context for a user session"""
    if session_id and session_id in USER_CONTEXT:
        return USER_CONTEXT[session_id].get('current_car')
    return None

def is_comparison_query(prompt):
    """Check if the prompt is a comparison query"""
    import re
    prompt_lower = prompt.lower()

    # Quick fix: if prompt contains "More about", it's definitely not a comparison
    if "more about" in prompt_lower:
        return False
    comparison_keywords = ['vs', 'versus', 'compare', 'comparison', 'between', 'which is better', 'best', 'difference']
    # Arena cars
    arena_cars = ['alto k10', 'swift', 'brezza', 'wagon-r', 'wagon r', 'dzire', 'celerio', 's-presso', 's presso', 'ertiga', 'eeco']
    # NEXA cars
    nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6', 'xl-6']
    # Tata Commercial vehicles
    tata_commercials = ['Small Trucks', 'Trucks', 'Vans/Buses']
    # Get all truck names from TRUCKS folder
    truck_names = get_all_truck_names()
    car_names = arena_cars + nexa_cars + tata_commercials + truck_names

    # Check if it has comparison keywords using word boundaries (except for 'vs' which is often standalone)
    has_comparison = False
    for keyword in comparison_keywords:
        if keyword == 'vs':
            # For 'vs', check if it's a standalone word or surrounded by spaces
            if re.search(r'\bvs\b', prompt_lower):
                has_comparison = True
                break
        else:
            # For other keywords, use word boundaries
            if re.search(r'\b' + re.escape(keyword) + r'\b', prompt_lower):
                has_comparison = True
                break

    # Special check for 'or' - only consider it a comparison keyword if it's between two car names
    if not has_comparison and ' or ' in prompt_lower:
        # Check if 'or' is between two car names
        words = prompt_lower.split()
        for i, word in enumerate(words):
            if word == 'or' and i > 0 and i < len(words) - 1:
                # Check if words before and after 'or' contain car names
                before_text = ' '.join(words[:i])
                after_text = ' '.join(words[i+1:])
                has_car_before = any(car in before_text for car in car_names)
                has_car_after = any(car in after_text for car in car_names)
                if has_car_before and has_car_after:
                    has_comparison = True
                    break

    has_car = any(car in prompt_lower for car in car_names)

    return has_comparison and has_car


def generate_detailed_car_comparison(car1_name, car2_name):
    """Compare two cars using actual data from arena_data.py and nexa_data.py"""
    try:
        # Import both arena and nexa data
        sys.path.insert(0, 'cars')
        from arena_data import cars_data as arena_cars
        from nexa_data import cars_data as nexa_cars

        # Find car data
        car1_data = None
        car2_data = None
        car1_key = None
        car2_key = None

        # Map car names to keys (Arena cars)
        arena_name_to_key = {
            'alto k10': 'alto_k10',
            'alto': 'alto_k10',
            'brezza': 'brezza',
            'swift': 'swift',
            'wagon-r': 'wagon_r',
            'wagon r': 'wagon_r',
            'dzire': 'dzire',
            'celerio': 'celerio',
            's-presso': 's_presso',
            's presso': 's_presso',
            'ertiga': 'ertiga',
            'eeco': 'eeco'
        }

        # Map car names to keys (NEXA cars)
        nexa_name_to_key = {
            'baleno': 'baleno',
            'ciaz': 'ciaz',
            'fronx': 'fronx',
            'grand vitara': 'grand_vitara',
            'ignis': 'ignis',
            'jimny': 'jimny',
            'xl6': 'xl6',
            'xl-6': 'xl6'
        }

        # Check Arena cars first
        car1_key = arena_name_to_key.get(car1_name.lower())
        car2_key = arena_name_to_key.get(car2_name.lower())

        if car1_key and car1_key in arena_cars:
            car1_data = arena_cars[car1_key]
        elif car1_name.lower() in nexa_name_to_key:
            car1_key = nexa_name_to_key[car1_name.lower()]
            if car1_key in nexa_cars:
                car1_data = nexa_cars[car1_key]

        if car2_key and car2_key in arena_cars:
            car2_data = arena_cars[car2_key]
        elif car2_name.lower() in nexa_name_to_key:
            car2_key = nexa_name_to_key[car2_name.lower()]
            if car2_key in nexa_cars:
                car2_data = nexa_cars[car2_key]

        if not car1_data or not car2_data:
            return None

        # Generate detailed comparison
        comparison = f"🚗 **{car1_data['name']} vs {car2_data['name']} Comparison**\n\n"

        # Category comparison
        comparison += f"📋 **Category:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['category']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['category']}\n\n"

        # Get variants using unified approach
        car1_variants = get_car_variants_unified(car1_data)
        car2_variants = get_car_variants_unified(car2_data)

        # Initialize price lists
        car1_prices = []
        car2_prices = []

        # Price comparison
        if car1_variants and car2_variants:
            # Extract prices from variants (works with both old and new format)
            import re

            def extract_price_from_variant(variant):
                # New format: direct price field
                if 'price' in variant:
                    price_str = variant['price']
                    price_match = re.search(r'(\d+\.?\d*)', price_str)
                    if price_match:
                        return float(price_match.group(1))

                # Old format: price in description
                if 'description' in variant:
                    description = variant['description']
                    price_match = re.search(r'₹([\d.]+)\s*Lakh', description)
                    if price_match:
                        return float(price_match.group(1))

                return 0.0

            car1_prices = [extract_price_from_variant(v) for v in car1_variants if extract_price_from_variant(v) > 0]
            car2_prices = [extract_price_from_variant(v) for v in car2_variants if extract_price_from_variant(v) > 0]

            if car1_prices and car2_prices:
                comparison += f"💰 **Price Range:**\n"
                comparison += f"• {car1_data.get('name', car1_key)}: ₹{min(car1_prices):.2f} - ₹{max(car1_prices):.2f} Lakh\n"
                comparison += f"• {car2_data.get('name', car2_key)}: ₹{min(car2_prices):.2f} - ₹{max(car2_prices):.2f} Lakh\n\n"
            else:
                comparison += f"💰 **Price Range:**\n"
                comparison += f"• {car1_data.get('name', car1_key)}: Price information available on request\n"
                comparison += f"• {car2_data.get('name', car2_key)}: Price information available on request\n\n"
        else:
            comparison += f"💰 **Price Range:**\n"
            comparison += f"• {car1_data.get('name', car1_key)}: Price information available on request\n"
            comparison += f"• {car2_data.get('name', car2_key)}: Price information available on request\n\n"

        # Basic comparison based on available JSON data
        comparison += f"🚗 **Vehicle Type:**\n"
        comparison += f"• {car1_name}: Premium {car1_name} with modern features\n"
        comparison += f"• {car2_name}: Stylish {car2_name} with advanced technology\n\n"

        comparison += f"⭐ **Key Highlights:**\n"
        comparison += f"• {car1_name}: Excellent fuel efficiency and reliability\n"
        comparison += f"• {car2_name}: Superior comfort and performance\n\n"

        # Variants count
        comparison += f"🔢 **Variants Available:**\n"
        comparison += f"• {car1_data.get('name', car1_key)}: {len(car1_variants)} variants\n"
        comparison += f"• {car2_data.get('name', car2_key)}: {len(car2_variants)} variants\n\n"

        # Key features comparison (if available in JSON data)
        comparison += f"✨ **Key Features:**\n"
        car1_name = car1_data.get('name', car1_key)
        car2_name = car2_data.get('name', car2_key)

        # For JSON data, we don't have direct features, so we'll use general info
        comparison += f"• {car1_name}: Premium features and modern technology\n"
        comparison += f"• {car2_name}: Advanced safety and comfort features\n\n"

        # Recommendation
        if car1_prices and car2_prices:
            if min(car1_prices) < min(car2_prices):
                comparison += f"💡 **Quick Insight:** {car1_name} is more budget-friendly, while {car2_name} offers premium features and styling."
            else:
                comparison += f"💡 **Quick Insight:** {car2_name} is more budget-friendly, while {car1_name} offers premium features and styling."
        else:
            comparison += f"💡 **Quick Insight:** Both {car1_name} and {car2_name} offer excellent value in their respective segments. Visit our showroom for detailed pricing and test drives."

        return comparison

    except Exception as e:
        logger.error(f"Error in car comparison: {e}")
        return None

def generate_detailed_truck_comparison(truck1_name, truck2_name):
    """Compare two trucks using actual data from Trucks folder"""
    try:
        # Import truck data from database folder
        sys.path.insert(0, 'database')
        from truck import trucks_data
        from small_trucks import small_trucks_data

        # Combine all truck data
        all_trucks_data = {**trucks_data, **small_trucks_data}

        # Create mapping from truck name (lower) to its key
        truck_name_to_key = {}
        for key, data in all_trucks_data.items():
            name = data.get('name', '').strip()
            name_lower = name.lower()
            truck_name_to_key[name_lower] = key
            truck_name_to_key[name_lower.replace(' ', '_')] = key
            truck_name_to_key[name_lower.replace('-', '_')] = key
            truck_name_to_key[key.lower()] = key
            if 'tata ' in name_lower:
                truck_name_to_key[name_lower.replace('tata ', '')] = key

        # Normalize input truck names
        truck1_lookup = CAR_DATA_REGISTRY.get(truck1_name)
        truck2_lookup = CAR_DATA_REGISTRY.get(truck2_name)

        # Lookup truck data using mappings
        truck1_key = truck_name_to_key.get(truck1_lookup.lower())
        truck2_key = truck_name_to_key.get(truck2_lookup.lower())

        if not truck1_key or not truck2_key:
            return f"❌ Could not find truck(s): {truck1_name if not truck1_key else ''} {truck2_name if not truck2_key else ''}".strip()

        truck1_data = all_trucks_data[truck1_key]
        truck2_data = all_trucks_data[truck2_key]

        # Start comparison message
        comparison = f"🚛 **{truck1_data['name']} vs {truck2_data['name']} Comparison**\n\n"

        # Category
        comparison += f"📋 **Category:**\n"
        comparison += f"• {truck1_data['name']}: {truck1_data['category']}\n"
        comparison += f"• {truck2_data['name']}: {truck2_data['category']}\n\n"

        # Fuel Types
        comparison += f"⛽ **Fuel Types:**\n"
        comparison += f"• {truck1_data['name']}: {', '.join(truck1_data['fuel_types'])}\n"
        comparison += f"• {truck2_data['name']}: {', '.join(truck2_data['fuel_types'])}\n\n"

        # Engine
        comparison += f"🔧 **Engine Specifications:**\n"
        truck1_engine = truck1_data.get('engine', {})
        truck2_engine = truck2_data.get('engine', {})

        if truck1_engine:
            fuel_type1 = list(truck1_engine.keys())[0]
            e1 = truck1_engine[fuel_type1]
            power1 = e1.get('power', e1.get('peak_power', 'N/A'))
            torque1 = e1.get('torque', e1.get('max_torque', 'N/A'))
            comparison += f"• {truck1_data['name']}: {power1} | {torque1}\n"

        if truck2_engine:
            fuel_type2 = list(truck2_engine.keys())[0]
            e2 = truck2_engine[fuel_type2]
            power2 = e2.get('power', e2.get('peak_power', 'N/A'))
            torque2 = e2.get('torque', e2.get('max_torque', 'N/A'))
            comparison += f"• {truck2_data['name']}: {power2} | {torque2}\n\n"

        # Specs
        comparison += f"📊 **Key Specifications:**\n"
        s1 = truck1_data.get('specifications', {})
        s2 = truck2_data.get('specifications', {})

        if 'gvw' in s1 and 'gvw' in s2:
            comparison += f"• **GVW:** {truck1_data['name']} - {s1['gvw']} | {truck2_data['name']} - {s2['gvw']}\n"

        if 'payload' in s1 and 'payload' in s2:
            comparison += f"• **Payload:** {truck1_data['name']} - {s1['payload']} | {truck2_data['name']} - {s2['payload']}\n"

        if 'fuel_tank_capacity' in s1 and 'fuel_tank_capacity' in s2:
            comparison += f"• **Fuel Tank:** {truck1_data['name']} - {s1['fuel_tank_capacity']} | {truck2_data['name']} - {s2['fuel_tank_capacity']}\n"

        if 'gradeability' in s1 and 'gradeability' in s2:
            comparison += f"• **Gradeability:** {truck1_data['name']} - {s1['gradeability']} | {truck2_data['name']} - {s2['gradeability']}\n"

        comparison += "\n"

        # Features
        if 'features' in truck1_data and 'features' in truck2_data:
            comparison += f"✨ **Key Features:**\n"
            comparison += f"• **{truck1_data['name']}:** {', '.join(truck1_data['features'][:3])}\n"
            comparison += f"• **{truck2_data['name']}:** {', '.join(truck2_data['features'][:3])}\n\n"

        # Warranty
        if 'warranty' in truck1_data and 'warranty' in truck2_data:
            comparison += f"🛡️ **Warranty:**\n"
            comparison += f"• {truck1_data['name']}: {truck1_data['warranty']}\n"
            comparison += f"• {truck2_data['name']}: {truck2_data['warranty']}\n\n"

        # Recommendation based on GVW
        def extract_number(text):
            match = re.search(r'(\d+)', text)
            return float(match.group(1)) if match else 0

        gvw1 = extract_number(s1.get('gvw', '0'))
        gvw2 = extract_number(s2.get('gvw', '0'))

        if gvw1 > gvw2:
            comparison += f"💡 **Quick Insight:** {truck1_data['name']} is better suited for heavier loads and long hauls.\n"
        elif gvw2 > gvw1:
            comparison += f"💡 **Quick Insight:** {truck2_data['name']} is better suited for heavier loads and long hauls.\n"
        else:
            comparison += f"💡 **Quick Insight:** Both trucks offer similar capabilities for their segment.\n"

        return comparison

    except Exception as e:
        logging.error(f"Error comparing trucks: {e}")
        return "⚠️ Sorry, something went wrong while comparing the trucks."


def compare_vehicles_unified(vehicle1_name, vehicle2_name):
    """Unified comparison function for all vehicle types (cars, trucks, vans, buses)"""
    try:
        # Get vehicle data for both vehicles
        vehicle1_data = get_vehicle_data_unified(vehicle1_name)
        vehicle2_data = get_vehicle_data_unified(vehicle2_name)

        if not vehicle1_data or not vehicle2_data:
            return None

        # Determine vehicle types for appropriate emoji and formatting
        vehicle1_type = get_vehicle_type(vehicle1_data)
        vehicle2_type = get_vehicle_type(vehicle2_data)

        # Choose appropriate emoji based on vehicle types
        emoji = get_comparison_emoji(vehicle1_type, vehicle2_type)

        # Generate detailed comparison
        comparison = f"{emoji} **{vehicle1_data['name']} vs {vehicle2_data['name']} Comparison**\n\n"

        # Category comparison
        comparison += f"📋 **Category:**\n"
        comparison += f"• {vehicle1_data['name']}: {vehicle1_data['category']}\n"
        comparison += f"• {vehicle2_data['name']}: {vehicle2_data['category']}\n\n"

        # Get variants and pricing
        vehicle1_variants = get_vehicle_variants_unified(vehicle1_data)
        vehicle2_variants = get_vehicle_variants_unified(vehicle2_data)

        vehicle1_prices = extract_prices_from_variants(vehicle1_variants)
        vehicle2_prices = extract_prices_from_variants(vehicle2_variants)

        # Price comparison (if available)
        if vehicle1_prices and vehicle2_prices:
            comparison += f"💰 **Price Range:**\n"
            comparison += f"• {vehicle1_data['name']}: ₹{min(vehicle1_prices):.2f} - ₹{max(vehicle1_prices):.2f} Lakh\n"
            comparison += f"• {vehicle2_data['name']}: ₹{min(vehicle2_prices):.2f} - ₹{max(vehicle2_prices):.2f} Lakh\n\n"

        # Engine comparison
        comparison += f"🔧 **Engine Specifications:**\n"
        comparison += format_engine_comparison(vehicle1_data, vehicle2_data)

        # Fuel types comparison
        comparison += f"⛽ **Fuel Options:**\n"
        comparison += f"• {vehicle1_data['name']}: {', '.join(vehicle1_data.get('fuel_types', []))}\n"
        comparison += f"• {vehicle2_data['name']}: {', '.join(vehicle2_data.get('fuel_types', []))}\n\n"

        # Specifications comparison (GVW, payload, etc.)
        comparison += format_specifications_comparison(vehicle1_data, vehicle2_data)

        # Mileage comparison (if available)
        comparison += format_mileage_comparison(vehicle1_data, vehicle2_data)

        # Variants count
        if vehicle1_variants or vehicle2_variants:
            comparison += f"🎯 **Available Variants:**\n"
            comparison += f"• {vehicle1_data['name']}: {len(vehicle1_variants)} variants\n"
            comparison += f"• {vehicle2_data['name']}: {len(vehicle2_variants)} variants\n\n"

        # Generate intelligent recommendation
        comparison += generate_vehicle_recommendation(vehicle1_data, vehicle2_data, vehicle1_prices, vehicle2_prices)

        return comparison

    except Exception as e:
        logger.error(f"Error in unified vehicle comparison: {e}")
        return None


def get_vehicle_data_unified(vehicle_name):
    """Get vehicle data from any source (cars, trucks, small trucks)"""
    try:
        vehicle_lower = vehicle_name.lower()

        # Try cars first (Arena and NEXA)
        sys.path.insert(0, 'cars')
        from arena_data import cars_data as arena_cars
        from nexa_data import cars_data as nexa_cars

        # Car name mappings
        arena_name_to_key = {
            'alto k10': 'alto_k10', 'alto': 'alto_k10', 'brezza': 'brezza',
            'swift': 'swift', 'wagon-r': 'wagon_r', 'wagon r': 'wagon_r',
            'dzire': 'dzire', 'celerio': 'celerio', 's-presso': 's_presso',
            's presso': 's_presso', 'ertiga': 'ertiga', 'eeco': 'eeco'
        }

        nexa_name_to_key = {
            'baleno': 'baleno', 'ciaz': 'ciaz', 'fronx': 'fronx',
            'grand vitara': 'grand_vitara', 'ignis': 'ignis', 'jimny': 'jimny',
            'xl6': 'xl6', 'xl-6': 'xl6'
        }

        # Check Arena cars
        if vehicle_lower in arena_name_to_key:
            key = arena_name_to_key[vehicle_lower]
            return arena_cars.get(key)

        # Check NEXA cars
        if vehicle_lower in nexa_name_to_key:
            key = nexa_name_to_key[vehicle_lower]
            return nexa_cars.get(key)

        # Try trucks and small trucks
        sys.path.insert(0, 'database')
        from truck import trucks_data
        from small_trucks import small_trucks_data

        # Combine all truck data
        all_trucks_data = {**trucks_data, **small_trucks_data}

        # Create truck name mappings
        truck_name_mappings = {}
        for key, data in all_trucks_data.items():
            truck_name = data.get('name', '').lower()
            if truck_name:
                truck_name_mappings[truck_name] = key
                # Add variations
                truck_name_mappings[truck_name.replace(' ', '-')] = key
                truck_name_mappings[truck_name.replace('-', ' ')] = key
                truck_name_mappings[truck_name.replace('tata ', '')] = key
                # Add key-based names
                truck_name_mappings[key.lower().replace('_', ' ')] = key
                truck_name_mappings[key.lower().replace('_', '-')] = key

        # Check trucks
        if vehicle_lower in truck_name_mappings:
            key = truck_name_mappings[vehicle_lower]
            return all_trucks_data.get(key)

        return None

    except Exception as e:
        logger.error(f"Error getting vehicle data for {vehicle_name}: {e}")
        return None


def get_vehicle_type(vehicle_data):
    """Determine vehicle type based on category"""
    category = vehicle_data.get('category', '').lower()

    if any(word in category for word in ['hatchback', 'sedan', 'suv', 'mpv']):
        return 'car'
    elif any(word in category for word in ['truck', 'pickup']):
        return 'truck'
    elif any(word in category for word in ['van', 'bus']):
        return 'van_bus'
    elif any(word in category for word in ['commercial', 'cargo']):
        return 'commercial'
    else:
        return 'vehicle'


def get_comparison_emoji(type1, type2):
    """Get appropriate emoji for comparison based on vehicle types"""
    if type1 == 'car' and type2 == 'car':
        return '🚗'
    elif type1 == 'truck' or type2 == 'truck':
        return '🚛'
    elif type1 == 'van_bus' or type2 == 'van_bus':
        return '🚐'
    elif type1 == 'commercial' or type2 == 'commercial':
        return '🚚'
    else:
        return '🚗'


def get_vehicle_variants_unified(vehicle_data):
    """Get variants from vehicle data in a unified format"""
    try:
        # Check for car variants (multiple possible keys)
        for key in vehicle_data.keys():
            if 'variants' in key:
                return vehicle_data[key]

        # For trucks, variants might not be explicitly defined
        # Return empty list if no variants found
        return []

    except Exception as e:
        logger.error(f"Error getting vehicle variants: {e}")
        return []


def extract_prices_from_variants(variants):
    """Extract prices from variants in a unified way"""
    try:
        prices = []
        import re

        for variant in variants:
            price = 0.0

            # New format: direct price field
            if 'price' in variant and variant['price']:
                price_str = variant['price']
                price_match = re.search(r'(\d+\.?\d*)', price_str)
                if price_match:
                    price = float(price_match.group(1))

            # Old format: price in description
            elif 'description' in variant:
                description = variant['description']
                price_match = re.search(r'₹([\d.]+)\s*Lakh', description)
                if price_match:
                    price = float(price_match.group(1))

            if price > 0:
                prices.append(price)

        return prices

    except Exception as e:
        logger.error(f"Error extracting prices from variants: {e}")
        return []


def format_engine_comparison(vehicle1_data, vehicle2_data):
    """Format engine comparison for both vehicles"""
    try:
        comparison = ""

        vehicle1_engine = vehicle1_data.get('engine', {})
        vehicle2_engine = vehicle2_data.get('engine', {})

        # Compare petrol engines
        if 'petrol' in vehicle1_engine:
            petrol1 = vehicle1_engine['petrol']
            displacement = petrol1.get('displacement', 'N/A')
            power = petrol1.get('max_power', petrol1.get('power', 'N/A'))
            torque = petrol1.get('max_torque', petrol1.get('torque', 'N/A'))
            comparison += f"• {vehicle1_data['name']} Petrol: {displacement} | {power} | {torque}\n"

        if 'petrol' in vehicle2_engine:
            petrol2 = vehicle2_engine['petrol']
            displacement = petrol2.get('displacement', 'N/A')
            power = petrol2.get('max_power', petrol2.get('power', 'N/A'))
            torque = petrol2.get('max_torque', petrol2.get('torque', 'N/A'))
            comparison += f"• {vehicle2_data['name']} Petrol: {displacement} | {power} | {torque}\n"

        # Compare diesel engines
        if 'diesel' in vehicle1_engine:
            diesel1 = vehicle1_engine['diesel']
            displacement = diesel1.get('displacement', 'N/A')
            power = diesel1.get('max_power', diesel1.get('power', 'N/A'))
            torque = diesel1.get('max_torque', diesel1.get('torque', 'N/A'))
            comparison += f"• {vehicle1_data['name']} Diesel: {displacement} | {power} | {torque}\n"

        if 'diesel' in vehicle2_engine:
            diesel2 = vehicle2_engine['diesel']
            displacement = diesel2.get('displacement', 'N/A')
            power = diesel2.get('max_power', diesel2.get('power', 'N/A'))
            torque = diesel2.get('max_torque', diesel2.get('torque', 'N/A'))
            comparison += f"• {vehicle2_data['name']} Diesel: {displacement} | {power} | {torque}\n"

        # Compare electric engines
        if 'electric' in vehicle1_engine:
            electric1 = vehicle1_engine['electric']
            motor_type = electric1.get('motor_type', 'N/A')
            power = electric1.get('peak_power', electric1.get('max_power', 'N/A'))
            torque = electric1.get('max_torque', 'N/A')
            comparison += f"• {vehicle1_data['name']} Electric: {motor_type} | {power} | {torque}\n"

        if 'electric' in vehicle2_engine:
            electric2 = vehicle2_engine['electric']
            motor_type = electric2.get('motor_type', 'N/A')
            power = electric2.get('peak_power', electric2.get('max_power', 'N/A'))
            torque = electric2.get('max_torque', 'N/A')
            comparison += f"• {vehicle2_data['name']} Electric: {motor_type} | {power} | {torque}\n"

        return comparison + "\n" if comparison else "Engine specifications not available\n\n"

    except Exception as e:
        logger.error(f"Error formatting engine comparison: {e}")
        return "Engine specifications not available\n\n"


def format_specifications_comparison(vehicle1_data, vehicle2_data):
    """Format specifications comparison (GVW, payload, dimensions, etc.)"""
    try:
        comparison = ""

        vehicle1_specs = vehicle1_data.get('specifications', {})
        vehicle2_specs = vehicle2_data.get('specifications', {})

        # Check if either vehicle has specifications
        if not vehicle1_specs and not vehicle2_specs:
            return ""

        comparison += f"📏 **Key Specifications:**\n"

        # GVW comparison
        if vehicle1_specs.get('gvw') or vehicle2_specs.get('gvw'):
            comparison += f"• GVW: {vehicle1_data['name']} - {vehicle1_specs.get('gvw', 'N/A')} | {vehicle2_data['name']} - {vehicle2_specs.get('gvw', 'N/A')}\n"

        # Payload comparison
        if vehicle1_specs.get('payload') or vehicle2_specs.get('payload'):
            comparison += f"• Payload: {vehicle1_data['name']} - {vehicle1_specs.get('payload', 'N/A')} | {vehicle2_data['name']} - {vehicle2_specs.get('payload', 'N/A')}\n"

        # Fuel tank capacity
        if vehicle1_specs.get('fuel_tank_capacity') or vehicle2_specs.get('fuel_tank_capacity'):
            comparison += f"• Fuel Tank: {vehicle1_data['name']} - {vehicle1_specs.get('fuel_tank_capacity', 'N/A')} | {vehicle2_data['name']} - {vehicle2_specs.get('fuel_tank_capacity', 'N/A')}\n"

        # Gradeability
        if vehicle1_specs.get('gradeability') or vehicle2_specs.get('gradeability'):
            comparison += f"• Gradeability: {vehicle1_data['name']} - {vehicle1_specs.get('gradeability', 'N/A')} | {vehicle2_data['name']} - {vehicle2_specs.get('gradeability', 'N/A')}\n"

        return comparison + "\n" if comparison != f"📏 **Key Specifications:**\n" else ""

    except Exception as e:
        logger.error(f"Error formatting specifications comparison: {e}")
        return ""


def format_mileage_comparison(vehicle1_data, vehicle2_data):
    """Format mileage comparison for both vehicles"""
    try:
        comparison = ""

        # Look for mileage data in various formats
        vehicle1_mileage = None
        vehicle2_mileage = None

        # Check for direct mileage in specifications
        vehicle1_specs = vehicle1_data.get('specifications', {})
        vehicle2_specs = vehicle2_data.get('specifications', {})

        if vehicle1_specs.get('mileage'):
            vehicle1_mileage = vehicle1_specs['mileage']
        if vehicle2_specs.get('mileage'):
            vehicle2_mileage = vehicle2_specs['mileage']

        # Check for car-style mileage data (key with vehicle name + " mileage")
        for key in vehicle1_data.keys():
            if 'mileage' in key.lower():
                vehicle1_mileage = vehicle1_data[key]
                break

        for key in vehicle2_data.keys():
            if 'mileage' in key.lower():
                vehicle2_mileage = vehicle2_data[key]
                break

        if vehicle1_mileage or vehicle2_mileage:
            comparison += f"⛽ **Mileage:**\n"

            if isinstance(vehicle1_mileage, dict):
                for fuel_type, mileage in vehicle1_mileage.items():
                    comparison += f"• {vehicle1_data['name']} {fuel_type.replace('_', ' ').title()}: {mileage}\n"
            elif vehicle1_mileage:
                comparison += f"• {vehicle1_data['name']}: {vehicle1_mileage}\n"

            if isinstance(vehicle2_mileage, dict):
                for fuel_type, mileage in vehicle2_mileage.items():
                    comparison += f"• {vehicle2_data['name']} {fuel_type.replace('_', ' ').title()}: {mileage}\n"
            elif vehicle2_mileage:
                comparison += f"• {vehicle2_data['name']}: {vehicle2_mileage}\n"

            comparison += "\n"

        return comparison

    except Exception as e:
        logger.error(f"Error formatting mileage comparison: {e}")
        return ""


def generate_vehicle_recommendation(vehicle1_data, vehicle2_data, vehicle1_prices, vehicle2_prices):
    """Generate intelligent recommendation based on vehicle data"""
    try:
        vehicle1_name = vehicle1_data['name']
        vehicle2_name = vehicle2_data['name']

        # Price-based recommendation
        if vehicle1_prices and vehicle2_prices:
            if min(vehicle1_prices) < min(vehicle2_prices):
                return f"💡 **Quick Insight:** {vehicle1_name} is more budget-friendly, while {vehicle2_name} offers premium features and capabilities."
            else:
                return f"💡 **Quick Insight:** {vehicle2_name} is more budget-friendly, while {vehicle1_name} offers premium features and capabilities."

        # Category-based recommendation
        vehicle1_category = vehicle1_data.get('category', '').lower()
        vehicle2_category = vehicle2_data.get('category', '').lower()

        if 'commercial' in vehicle1_category or 'truck' in vehicle1_category:
            if 'commercial' in vehicle2_category or 'truck' in vehicle2_category:
                # Both are commercial vehicles
                vehicle1_specs = vehicle1_data.get('specifications', {})
                vehicle2_specs = vehicle2_data.get('specifications', {})

                # Compare GVW for recommendation
                import re
                def extract_number(text):
                    if not text:
                        return 0
                    match = re.search(r'(\d+)', str(text))
                    return float(match.group(1)) if match else 0

                gvw1 = extract_number(vehicle1_specs.get('gvw', '0'))
                gvw2 = extract_number(vehicle2_specs.get('gvw', '0'))

                if gvw1 > gvw2:
                    return f"💡 **Quick Insight:** {vehicle1_name} is better suited for heavier loads and long hauls."
                elif gvw2 > gvw1:
                    return f"💡 **Quick Insight:** {vehicle2_name} is better suited for heavier loads and long hauls."
                else:
                    return f"💡 **Quick Insight:** Both vehicles offer similar capabilities for their segment."
            else:
                return f"💡 **Quick Insight:** {vehicle1_name} is ideal for commercial use, while {vehicle2_name} is perfect for personal transportation."
        elif 'commercial' in vehicle2_category or 'truck' in vehicle2_category:
            return f"💡 **Quick Insight:** {vehicle2_name} is ideal for commercial use, while {vehicle1_name} is perfect for personal transportation."

        # Default recommendation
        return f"💡 **Quick Insight:** Both {vehicle1_name} and {vehicle2_name} offer excellent value in their respective segments. Visit our showroom for detailed information and test drives."

    except Exception as e:
        logger.error(f"Error generating vehicle recommendation: {e}")
        return f"💡 **Quick Insight:** Both vehicles offer excellent value in their respective segments. Visit our showroom for detailed information."


def get_all_truck_names():
    """Get all truck names from TRUCKS folder data"""
    try:
        # Import truck data from database folder
        sys.path.insert(0, 'database')
        from truck import trucks_data
        from small_trucks import small_trucks_data

        # Combine all truck data
        all_trucks_data = {**trucks_data, **small_trucks_data}

        # Extract truck names
        truck_names = []
        for key, data in all_trucks_data.items():
            truck_name = data.get('name', '').lower()
            if truck_name:
                truck_names.append(truck_name)
                # Add variations
                truck_names.append(truck_name.replace(' ', '-'))
                truck_names.append(truck_name.replace('-', ' '))
                truck_names.append(truck_name.replace('tata ', ''))
                truck_names.append(truck_name.replace('ace pro', 'ace-pro'))
                truck_names.append(truck_name.replace('ace-pro', 'ace pro'))
                # Add key-based names
                truck_names.append(key.lower().replace('_', ' '))
                truck_names.append(key.lower().replace('_', '-'))
                # Add specific common variations
                if 'ultra t.6' in truck_name:
                    truck_names.append('ultra t.6')
                    truck_names.append('ultra t6')
                if 'lpt' in truck_name:
                    # Add LPT variations without "Tata"
                    lpt_name = truck_name.replace('tata ', '')
                    truck_names.append(lpt_name)

        # Remove duplicates and return
        return list(set(truck_names))

    except Exception as e:
        logger.error(f"Error getting truck names: {e}")
        return []


class UniversalVehicleManager:
    """Universal Vehicle Manager - Works for ALL vehicle types (cars, trucks, vans, buses, etc.)"""

    def __init__(self):
        self.all_vehicles = {}
        self.vehicle_categories = {}
        self.vehicle_mappings = {}
        self._load_all_vehicles()

    def _load_all_vehicles(self):
        """Load ALL vehicles from ALL sources"""
        try:
            # 1. Load cars from CAR_DATA_REGISTRY
            for car_name in CAR_DATA_REGISTRY.keys():
                vehicle_key = car_name.lower()
                self.all_vehicles[vehicle_key] = {
                    'name': car_name,
                    'type': 'car',
                    'source': 'CAR_DATA_REGISTRY'
                }

                # Add to categories
                if 'car' not in self.vehicle_categories:
                    self.vehicle_categories['car'] = []
                self.vehicle_categories['car'].append(car_name)

            # 2. Load trucks from truck data
            try:
                from database.truck import trucks_data
                from database.small_trucks import small_trucks_data

                all_trucks_data = {**trucks_data, **small_trucks_data}

                for _, truck_data in all_trucks_data.items():
                    truck_name = truck_data.get('name', '')
                    if truck_name:
                        vehicle_key = truck_name.lower()
                        category = truck_data.get('category', '')

                        self.all_vehicles[vehicle_key] = {
                            'name': truck_name,
                            'type': 'truck',
                            'category': category,
                            'source': 'trucks_data'
                        }

                        # Add to categories
                        if 'truck' not in self.vehicle_categories:
                            self.vehicle_categories['truck'] = []
                        self.vehicle_categories['truck'].append(truck_name)

                        # Add category-specific grouping
                        if 'Light Commercial Vehicle' in category:
                            if 'small_truck' not in self.vehicle_categories:
                                self.vehicle_categories['small_truck'] = []
                            self.vehicle_categories['small_truck'].append(truck_name)
                        elif 'Heavy' in category:
                            if 'heavy_truck' not in self.vehicle_categories:
                                self.vehicle_categories['heavy_truck'] = []
                            self.vehicle_categories['heavy_truck'].append(truck_name)

            except Exception as e:
                logger.warning(f"Could not load truck data: {e}")

            # 3. Setup common name mappings
            self.vehicle_mappings = {
                'alto': 'Alto K10',
                'wagon r': 'Wagon-R',
                's presso': 'S-Presso',
                'xl-6': 'XL6',
                'ace pro': 'Ace-Pro EV',
                'ace': 'Ace-Pro EV',
                'intra': 'Intra V10',
                'yodha': 'Yodha-CNG',
                'ultra': 'Tata ULTRA T.6',
                'small truck': 'Ace-Pro EV',
                'mini truck': 'Ace-Pro EV',
                'pickup': 'Yodha-CNG',
                'commercial vehicle': 'Ace-Pro EV'
            }

            logger.info(f"✅ Universal Vehicle Manager loaded {len(self.all_vehicles)} vehicles")

        except Exception as e:
            logger.error(f"Error loading vehicles: {e}")

    def get_all_vehicles(self, vehicle_type=None):
        """Get all vehicles, optionally filtered by type"""
        if vehicle_type:
            return self.vehicle_categories.get(vehicle_type, [])
        return [info['name'] for info in self.all_vehicles.values()]

    def get_vehicle_by_name(self, name):
        """Get vehicle info by name (with smart matching)"""
        name_lower = name.lower()

        # Direct match
        if name_lower in self.all_vehicles:
            return self.all_vehicles[name_lower]

        # Check mappings
        if name_lower in self.vehicle_mappings:
            mapped_name = self.vehicle_mappings[name_lower]
            return self.get_vehicle_by_name(mapped_name)

        # Partial match
        for vehicle_key, vehicle_info in self.all_vehicles.items():
            if name_lower in vehicle_key or vehicle_key in name_lower:
                return vehicle_info

        return None

    def search_vehicles(self, query, limit=10):
        """Search vehicles by query"""
        query_lower = query.lower()
        results = []

        for vehicle_key, vehicle_info in self.all_vehicles.items():
            if query_lower in vehicle_key or query_lower in vehicle_info['name'].lower():
                results.append(vehicle_info['name'])
                if len(results) >= limit:
                    break

        return results

    def get_vehicles_by_category(self, category):
        """Get vehicles by category (car, truck, small_truck, heavy_truck, etc.)"""
        return self.vehicle_categories.get(category, [])

    def get_vehicle_categories(self):
        """Get all available categories"""
        return list(self.vehicle_categories.keys())


# Global instance
universal_vehicle_manager = UniversalVehicleManager()


def get_all_vehicles_universal(vehicle_type=None):
    """Universal function to get all vehicles - replaces separate car/truck functions"""
    return universal_vehicle_manager.get_all_vehicles(vehicle_type)


def select_intelligent_vehicles(vehicles, query_lower, category, budget):
    """
    Intelligently select vehicles based on user query context and preferences
    """
    if not vehicles:
        return []

    # Define query context patterns and their preferred vehicles
    context_patterns = {
        # Family-oriented queries
        'family': ['Ertiga', 'XL6', 'Brezza', 'Grand Vitara', 'Dzire'],
        'family car': ['Ertiga', 'XL6', 'Brezza', 'Grand Vitara', 'Dzire'],
        'kids': ['Ertiga', 'XL6', 'Brezza', 'Grand Vitara'],
        'children': ['Ertiga', 'XL6', 'Brezza', 'Grand Vitara'],

        # City driving queries
        'city': ['Alto K10', 'Celerio', 'Swift', 'Baleno', 'Wagon-R'],
        'city driving': ['Alto K10', 'Celerio', 'Swift', 'Baleno', 'Wagon-R'],
        'traffic': ['Alto K10', 'Celerio', 'Swift', 'Wagon-R'],
        'parking': ['Alto K10', 'Celerio', 'Swift', 'Wagon-R'],

        # Highway/long distance queries
        'highway': ['Grand Vitara', 'Brezza', 'Ciaz', 'Fronx', 'Dzire'],
        'long drive': ['Grand Vitara', 'Brezza', 'Ciaz', 'Fronx', 'Dzire'],
        'travel': ['Grand Vitara', 'Brezza', 'Ciaz', 'Fronx', 'Ertiga'],
        'tour': ['Grand Vitara', 'Brezza', 'Ciaz', 'Fronx', 'Ertiga'],

        # Fuel efficiency queries
        'mileage': ['Alto K10', 'Celerio', 'Dzire', 'Swift', 'Wagon-R'],
        'fuel efficient': ['Alto K10', 'Celerio', 'Dzire', 'Swift', 'Wagon-R'],
        'petrol': ['Alto K10', 'Swift', 'Baleno', 'Fronx', 'Grand Vitara'],
        'cng': ['Ertiga', 'Dzire', 'Wagon-R', 'Eeco'],

        # Space/comfort queries
        'spacious': ['Ertiga', 'XL6', 'Grand Vitara', 'Brezza', 'Ciaz'],
        'comfort': ['Grand Vitara', 'Ciaz', 'Brezza', 'XL6', 'Ertiga'],
        'luxury': ['Grand Vitara', 'Ciaz', 'XL6', 'Baleno'],

        # First car queries
        'first car': ['Alto K10', 'Celerio', 'Swift', 'Wagon-R'],
        'beginner': ['Alto K10', 'Celerio', 'Swift', 'Wagon-R'],
        'new driver': ['Alto K10', 'Celerio', 'Swift', 'Wagon-R'],

        # Business queries
        'business': ['Dzire', 'Ciaz', 'Brezza', 'Grand Vitara'],
        'office': ['Dzire', 'Ciaz', 'Brezza', 'Swift', 'Baleno'],
        'professional': ['Dzire', 'Ciaz', 'Brezza', 'Grand Vitara'],

        # Adventure/outdoor queries
        'adventure': ['Jimny', 'Grand Vitara', 'Brezza', 'Fronx'],
        'off road': ['Jimny', 'Grand Vitara', 'Brezza'],
        'mountains': ['Jimny', 'Grand Vitara', 'Brezza', 'Fronx'],

        # Style/looks queries
        'stylish': ['Fronx', 'Grand Vitara', 'Baleno', 'Ciaz', 'Jimny'],
        'sporty': ['Fronx', 'Swift', 'Baleno', 'Jimny'],
        'modern': ['Fronx', 'Grand Vitara', 'Baleno', 'Brezza'],
    }

    # Score vehicles based on query context
    vehicle_scores = {}
    for vehicle in vehicles:
        vehicle_scores[vehicle] = 0

    # Apply context-based scoring
    for pattern, preferred_vehicles in context_patterns.items():
        if pattern in query_lower:
            for vehicle in preferred_vehicles:
                if vehicle in vehicle_scores:
                    vehicle_scores[vehicle] += 10  # High priority for context match

    # Apply category-specific preferences
    if category == 'suv':
        suv_preferences = ['Grand Vitara', 'Brezza', 'Fronx', 'Jimny', 'S-Presso']
        for vehicle in suv_preferences:
            if vehicle in vehicle_scores:
                vehicle_scores[vehicle] += 5
    elif category == 'hatchback':
        hatchback_preferences = ['Swift', 'Baleno', 'Alto K10', 'Celerio', 'Wagon-R', 'Ignis']
        for vehicle in hatchback_preferences:
            if vehicle in vehicle_scores:
                vehicle_scores[vehicle] += 5
    elif category == 'sedan':
        sedan_preferences = ['Dzire', 'Ciaz']
        for vehicle in sedan_preferences:
            if vehicle in vehicle_scores:
                vehicle_scores[vehicle] += 5

    # Sort vehicles by score (highest first), then alphabetically
    sorted_vehicles = sorted(vehicles, key=lambda v: (-vehicle_scores.get(v, 0), v))

    return sorted_vehicles


def handle_universal_budget_query(prompt, user_id=None):
    """Universal budget handler - works for ALL vehicle types (cars, trucks, vans, buses, etc.)"""
    prompt_lower = prompt.lower()

    # Extract price limit and query type
    import re
    price_limit = None
    query_type = "under"  # default

    # Comprehensive price patterns for all query types
    price_patterns = {
        'under': [
            r'under\s+(\d+)\s*l(?:akh)?s?',
            r'below\s+(\d+)\s*l(?:akh)?s?',
            r'budget\s+(\d+)\s*l(?:akh)?s?',
            r'within\s+(\d+)\s*l(?:akh)?s?',
            r'up\s+to\s+(\d+)\s*l(?:akh)?s?',
            r'maximum\s+(\d+)\s*l(?:akh)?s?',
            r'max\s+(\d+)\s*l(?:akh)?s?',
            r'i\s+have\s+(\d+)l',  # "i have 10l"
            r'my\s+budget\s+is\s+(\d+)\s*l(?:akh)?s?',
            r'budget\s+of\s+(\d+)\s*l(?:akh)?s?'
        ],
        'from': [
            r'from\s+(\d+)\s*l(?:akh)?s?',
            r'starting\s+(\d+)\s*l(?:akh)?s?',
            r'above\s+(\d+)\s*l(?:akh)?s?',
            r'over\s+(\d+)\s*l(?:akh)?s?',
            r'minimum\s+(\d+)\s*l(?:akh)?s?',
            r'min\s+(\d+)\s*l(?:akh)?s?'
        ],
        'range': [
            r'between\s+(\d+)\s*(?:and|to|-)\s*(\d+)\s*l(?:akh)?s?',
            r'(\d+)\s*(?:to|-)\s*(\d+)\s*l(?:akh)?s?'
        ]
    }

    # Try to extract price information
    for query_type_key, patterns in price_patterns.items():
        for pattern in patterns:
            match = re.search(pattern, prompt_lower)
            if match:
                query_type = query_type_key
                if query_type == 'range':
                    price_limit = (int(match.group(1)), int(match.group(2)))
                else:
                    price_limit = int(match.group(1))
                break
        if price_limit:
            break

    if not price_limit:
        return None

    # Determine vehicle type and specific category from query
    vehicle_type = None
    specific_category = None

    vehicle_keywords = {
        'car': ['car', 'cars', 'family car', 'arena', 'nexa'],
        'suv': ['suv', 'suvs'],
        'hatchback': ['hatchback', 'hatchbacks'],
        'sedan': ['sedan', 'sedans'],
        'truck': ['truck', 'trucks', 'lorry', 'lorries', 'commercial', 'pickup', 'cargo', 'delivery', 'goods carrier', 'mini truck', 'small truck'],
        'electric': ['electric', 'ev', 'e-vehicle'],
        'cng': ['cng', 'compressed natural gas'],
        'diesel': ['diesel'],
        'petrol': ['petrol', 'gasoline']
    }

    # Find the most specific vehicle type
    for vtype, keywords in vehicle_keywords.items():
        if any(keyword in prompt_lower for keyword in keywords):
            if vtype in ['suv', 'hatchback', 'sedan']:
                vehicle_type = 'car'
                specific_category = vtype
            else:
                vehicle_type = vtype
            break

    # If no specific type found, default to cars for budget queries
    if not vehicle_type:
        vehicle_type = 'car'

    # Get vehicles based on type
    if vehicle_type in ['car']:
        vehicles = universal_vehicle_manager.get_vehicles_by_category('car')
    elif vehicle_type in ['truck']:
        vehicles = universal_vehicle_manager.get_vehicles_by_category('truck')
    elif vehicle_type in ['electric', 'cng', 'diesel', 'petrol']:
        # For fuel-based queries, search across all vehicles
        vehicles = universal_vehicle_manager.search_vehicles(vehicle_type, limit=20)
    else:
        vehicles = universal_vehicle_manager.get_all_vehicles()

    if not vehicles:
        return None

    # Filter by specific category if specified (SUV, hatchback, sedan)
    if specific_category:
        filtered_vehicles = []
        for vehicle_name in vehicles:
            # Get vehicle data to check category using the unified function
            vehicle_data = get_vehicle_data_unified(vehicle_name)
            if vehicle_data:
                vehicle_category = vehicle_data.get('category', '').lower()

                if specific_category == 'suv' and 'suv' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)

                elif specific_category == 'hatchback' and 'hatchback' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)

                elif specific_category == 'sedan' and 'sedan' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)

            else:
                logger.warning(f"⚠️ No data found for vehicle: {vehicle_name}")


        vehicles = filtered_vehicles

    if not vehicles:
        return None

    # Filter vehicles by budget (this would need actual price data integration)
    # For now, return appropriate vehicles with budget message

    # Intelligent button selection based on user query context
    vehicles = select_intelligent_vehicles(vehicles, prompt_lower, specific_category, price_limit)

    # Generate response based on query type and vehicle type/category
    if vehicle_type == 'car':
        if specific_category == 'suv':
            if query_type == 'under':
                title = f"🚙 **SUVs under ₹{price_limit} Lakh**"
                description = f"Here are excellent SUVs within your budget of ₹{price_limit} Lakh. SUVs offer higher ground clearance, spacious interiors, and commanding road presence:"
            elif query_type == 'from':
                title = f"🚙 **SUVs from ₹{price_limit} Lakh**"
                description = f"Here are premium SUVs starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚙 **SUVs between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are SUVs in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        elif specific_category == 'hatchback':
            if query_type == 'under':
                title = f"🚗 **Hatchbacks under ₹{price_limit} Lakh**"
                description = f"Here are excellent hatchbacks within your budget of ₹{price_limit} Lakh. Hatchbacks are compact, fuel-efficient, and perfect for city driving:"
            elif query_type == 'from':
                title = f"🚗 **Hatchbacks from ₹{price_limit} Lakh**"
                description = f"Here are premium hatchbacks starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Hatchbacks between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are hatchbacks in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        elif specific_category == 'sedan':
            if query_type == 'under':
                title = f"🚗 **Sedans under ₹{price_limit} Lakh**"
                description = f"Here are excellent sedans within your budget of ₹{price_limit} Lakh. Sedans offer spacious interiors, comfort, and elegant styling:"
            elif query_type == 'from':
                title = f"🚗 **Sedans from ₹{price_limit} Lakh**"
                description = f"Here are premium sedans starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Sedans between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are sedans in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        else:
            if query_type == 'under':
                title = f"🚗 **Cars under ₹{price_limit} Lakh**"
                description = f"Here are excellent cars within your budget of ₹{price_limit} Lakh:"
            elif query_type == 'from':
                title = f"🚗 **Cars from ₹{price_limit} Lakh**"
                description = f"Here are premium cars starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Cars between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are cars in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
    elif vehicle_type == 'truck':
        if query_type == 'under':
            title = f"🚛 **Trucks under ₹{price_limit} Lakh**"
            description = f"Here are commercial vehicles within your budget of ₹{price_limit} Lakh:"
        elif query_type == 'from':
            title = f"🚛 **Trucks from ₹{price_limit} Lakh**"
            description = f"Here are heavy-duty trucks starting from ₹{price_limit} Lakh:"
        else:  # range
            title = f"🚛 **Trucks between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
            description = f"Here are trucks in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
    else:
        title = f"🚗 **{vehicle_type.title()} vehicles in your budget**"
        description = f"Here are {vehicle_type} vehicles matching your budget criteria:"

    # Limit vehicles for display
    display_vehicles = vehicles[:10]

    message = f"{title}\n\n{description}\n\n"

    # Add vehicle preview
    for i, vehicle in enumerate(display_vehicles[:5], 1):
        message += f"{i}. {vehicle}\n"

    if len(vehicles) > 5:
        message += f"\n📋 Total: {len(vehicles)} models available in your budget\n"

    message += "\n*Which vehicle would you like to explore?*"

    return {
        "message": message,
        "buttons": [{
            "data": display_vehicles,
            "data_type": "list",
            "message": "📋 Here are some vehicles you can explore. Please select one:"

        }]
    }


def handle_universal_vehicle_query(prompt, user_id=None):
    """Universal vehicle query handler - replaces all separate vehicle-type handlers"""
    prompt_lower = prompt.lower()

    # Define query patterns and their corresponding vehicle categories
    query_patterns = {
        # Car queries
        'family car': 'car',
        'family': 'car',
        'arena cars': 'car',
        'nexa cars': 'car',
        'car': 'car',
        'hatchback': 'car',
        'sedan': 'car',
        'suv': 'car',

        # Van queries
        'school van': 'van',
        'tourist van': 'van',
        'ambulance van': 'van',
        'ambulance': 'van',
        'van': 'van',
        'vans': 'van',

        # Bus queries
        'school bus': 'bus',
        'staff bus': 'bus',
        'contract bus': 'bus',
        'bus': 'bus',
        'buses': 'bus',

        # Truck queries
        'small truck': 'small_truck',
        'mini truck': 'small_truck',
        'compact truck': 'small_truck',
        'truck': 'truck',
        'lorry': 'heavy_truck',
        'heavy truck': 'heavy_truck',
        'commercial vehicle': 'truck',
        'commercial': 'truck',
        'pickup': 'truck',
        'cargo': 'truck',
        'delivery': 'truck',

        # Fuel-based queries
        'electric': None,  # Will search across all types
        'cng': None,
        'diesel': None,
        'petrol': None
    }

    # Find matching pattern with priority (longer matches first)
    matched_pattern = None
    matched_category = None

    sorted_patterns = sorted(query_patterns.items(), key=lambda x: len(x[0]), reverse=True)
    for pattern, category in sorted_patterns:
        if pattern in prompt_lower:
            matched_pattern = pattern
            matched_category = category
            break

    if matched_pattern:
        # Get vehicles based on category
        if matched_category:
            vehicles = universal_vehicle_manager.get_vehicles_by_category(matched_category)
        else:
            # For fuel-based queries, search across all vehicles
            vehicles = universal_vehicle_manager.search_vehicles(matched_pattern, limit=15)

        if vehicles:
            # Generate appropriate message
            if matched_pattern in ['family car', 'family']:
                message_title = "👨‍👩‍👧‍👦 **Perfect family cars for you!**"
                message_desc = "Spacious, safe, and comfortable vehicles designed for family journeys."
            elif matched_pattern in ['small truck', 'mini truck', 'compact truck']:
                message_title = "🚛 **Small trucks perfect for daily use!**"
                message_desc = "Compact and efficient trucks ideal for daily business operations and city deliveries."
            elif matched_pattern in ['van', 'vans']:
                message_title = "🚐 **Vans for all your transport needs!**"
                message_desc = "Versatile vans perfect for passenger transport, cargo delivery, or specialized services. Our range includes school vans, tourist vans, and ambulance vans."
            elif matched_pattern in ['school van']:
                message_title = "🚌 **School vans for safe student transport!**"
                message_desc = "Specially designed school vans with safety features for reliable student transportation."
            elif matched_pattern in ['tourist van']:
                message_title = "🚐 **Tourist vans for comfortable travel!**"
                message_desc = "Spacious and comfortable vans perfect for tourism and travel businesses."
            elif matched_pattern in ['ambulance van', 'ambulance']:
                message_title = "🚑 **Ambulance vans for medical services!**"
                message_desc = "Specialized ambulance vans equipped for medical emergencies and patient care."
            elif matched_pattern in ['bus', 'buses']:
                message_title = "🚌 **Buses for passenger transport!**"
                message_desc = "Reliable buses designed for school transport, staff transport, and contract services."
            elif matched_pattern in ['school bus']:
                message_title = "🚌 **School buses for educational institutions!**"
                message_desc = "Safe and reliable school buses designed for student transportation."
            elif matched_pattern in ['staff bus', 'contract bus']:
                message_title = "🚌 **Staff and contract buses!**"
                message_desc = "Comfortable buses perfect for employee transport and contract services."
            elif matched_pattern in ['truck', 'commercial', 'commercial vehicle']:
                message_title = "🚛 **Commercial vehicles for your business needs!**"
                message_desc = "From light to heavy-duty, our trucks are built for Indian roads and business requirements."
            elif matched_pattern in ['lorry', 'heavy truck']:
                message_title = "🚛 **Heavy-duty vehicles for serious cargo transport!**"
                message_desc = "Powerful trucks designed for heavy loads and long-distance transport."
            elif matched_pattern == 'electric':
                message_title = "⚡ **Go green with electric vehicles!**"
                message_desc = "Zero emissions, low running costs—perfect for modern needs."
            elif matched_pattern == 'cng':
                message_title = "🌿 **Explore CNG-powered vehicles!**"
                message_desc = "Cleaner and more cost-effective for day-to-day use."
            else:
                message_title = f"🚗 **{matched_pattern.title()} vehicles for you!**"
                message_desc = "Explore our range of vehicles designed for your specific needs."

            message = f"{message_title}\n{message_desc}\n\n"

            # Add vehicle list preview
            vehicle_preview = vehicles[:5]  # Show first 5
            for i, vehicle in enumerate(vehicle_preview, 1):
                message += f"{i}. {vehicle}\n"

            if len(vehicles) > 5:
                message += f"\n📋 Total: {len(vehicles)} models available\n"

            message += "\n*Which vehicle interests you?*"

            return {
                "message": message,
                "buttons": [{
                    "data": vehicles[:10],  # Limit to 10 for WhatsApp compatibility
                    "data_type": "list",
                    "message": "📋 Here are some vehicles you can explore. Please select one:"

                }]
            }

    return None


def find_best_vehicle_match(query, all_vehicles, vehicle_name_mappings):
    """Find the best matching vehicle for a query string"""
    query = query.lower().strip()

    # First, check direct mappings
    if query in vehicle_name_mappings:
        return vehicle_name_mappings[query]

    # Then, check exact matches in vehicle registry
    if query in all_vehicles:
        return all_vehicles[query]

    # Try partial matches - find vehicles that contain the query or vice versa
    best_matches = []
    for vehicle_key, vehicle_name in all_vehicles.items():
        if query in vehicle_key or vehicle_key in query:
            # Prioritize shorter matches (more specific)
            score = abs(len(vehicle_key) - len(query))
            best_matches.append((score, vehicle_name))

    # Sort by score (lower is better) and return the best match
    if best_matches:
        best_matches.sort(key=lambda x: x[0])
        return best_matches[0][1]

    # Check alternative name mappings for partial matches
    for alt_name, canonical_name in vehicle_name_mappings.items():
        if query in alt_name or alt_name in query:
            return canonical_name

    return None


def generate_universal_comparison_buttons(prompt):
    """Universal function to generate buttons for comparison queries - works for ALL vehicle types"""
    prompt_lower = prompt.lower()

    # Get all available vehicles from all sources
    all_vehicles = {}

    # 1. Add all cars from CAR_DATA_REGISTRY
    for car_name in CAR_DATA_REGISTRY.keys():
        all_vehicles[car_name.lower()] = car_name

    # 2. Add all trucks from truck data
    try:
        from database.truck import trucks_data
        from database.small_trucks import small_trucks_data

        # Combine all truck data
        all_trucks_data = {**trucks_data, **small_trucks_data}

        for _, truck_data in all_trucks_data.items():
            truck_name = truck_data.get('name', '')
            if truck_name:
                all_vehicles[truck_name.lower()] = truck_name
    except Exception as e:
        logger.warning(f"Could not load truck data: {e}")
        pass

    # 3. Add alternative name mappings for better matching
    vehicle_name_mappings = {
        # Car mappings
        'alto': 'Alto K10',
        'wagon r': 'Wagon-R',
        's presso': 'S-Presso',
        'xl-6': 'XL6',
        'xl 6': 'XL6',

        # Truck mappings
        'ace pro': 'Ace-Pro EV',
        'ace': 'Ace-Pro EV',
        'intra': 'Intra V10',
        'yodha': 'Yodha-CNG',
        'ultra': 'Tata ULTRA T.6',
        'lpt': 'Tata LPT 709G',
        'signa': 'Tata Signa 1923.K',
        'prima': 'Tata Prima 2830K HRT',

        # Commercial vehicle mappings
        'small truck': 'Ace-Pro EV',
        'mini truck': 'Ace-Pro EV',
        'pickup': 'Yodha-CNG',
        'pickup truck': 'Yodha-CNG',
        'commercial vehicle': 'Ace-Pro EV',
        'delivery truck': 'Ace-Pro EV',
        'cargo truck': 'Intra V10'
    }

    # Extract mentioned vehicles using intelligent matching
    mentioned_vehicles = []

    # Advanced pattern matching for comparison queries
    import re

    # Extract potential vehicle names using patterns
    comparison_patterns = [
        r'compare\s+([a-zA-Z0-9\s\-\.]+?)\s+(?:vs|and|with)\s+([a-zA-Z0-9\s\-\.]+?)(?:\s|$)',
        r'([a-zA-Z0-9\s\-\.]+?)\s+vs\s+([a-zA-Z0-9\s\-\.]+?)(?:\s|$)',
        r'([a-zA-Z0-9\s\-\.]+?)\s+versus\s+([a-zA-Z0-9\s\-\.]+?)(?:\s|$)',
        r'difference\s+between\s+([a-zA-Z0-9\s\-\.]+?)\s+and\s+([a-zA-Z0-9\s\-\.]+?)(?:\s|$)'
    ]

    # Try to extract exactly 2 vehicles from comparison patterns
    extracted_vehicles = []
    for pattern in comparison_patterns:
        matches = re.findall(pattern, prompt_lower, re.IGNORECASE)
        for match in matches:
            vehicle1_query, vehicle2_query = match[0].strip(), match[1].strip()

            # Find best match for vehicle1
            vehicle1_match = find_best_vehicle_match(vehicle1_query, all_vehicles, vehicle_name_mappings)
            if vehicle1_match:
                extracted_vehicles.append(vehicle1_match)

            # Find best match for vehicle2
            vehicle2_match = find_best_vehicle_match(vehicle2_query, all_vehicles, vehicle_name_mappings)
            if vehicle2_match:
                extracted_vehicles.append(vehicle2_match)

            # If we found both vehicles, use them (remove duplicates)
            if len(extracted_vehicles) >= 2:
                # Remove duplicates while preserving order
                unique_vehicles = []
                for vehicle in extracted_vehicles:
                    if vehicle not in unique_vehicles:
                        unique_vehicles.append(vehicle)

                if len(unique_vehicles) >= 2:
                    mentioned_vehicles = unique_vehicles[:2]
                    break
                elif len(unique_vehicles) == 1:
                    # If only one unique vehicle found, continue searching
                    extracted_vehicles = unique_vehicles

        if mentioned_vehicles:
            break

    # Fallback: If pattern matching didn't work, try simple keyword matching
    if not mentioned_vehicles:
        # First, try exact matches with all vehicles
        for vehicle_key, vehicle_name in all_vehicles.items():
            if vehicle_key in prompt_lower:
                if vehicle_name not in mentioned_vehicles:
                    mentioned_vehicles.append(vehicle_name)

        # Then, try alternative name mappings
        for alt_name, canonical_name in vehicle_name_mappings.items():
            if alt_name in prompt_lower:
                if canonical_name not in mentioned_vehicles:
                    mentioned_vehicles.append(canonical_name)

    # Return the mentioned vehicles as buttons if we found any
    if mentioned_vehicles:
        return [{
            "data": mentioned_vehicles[:10],  # Limit to 10 for WhatsApp compatibility
            "data_type": "list",
            "message": "Select a vehicle for detailed specifications:"
        }]

    return []


def generate_comparison_buttons(prompt):
    """Legacy function - now calls the universal comparison function"""
    return generate_universal_comparison_buttons(prompt)


def generate_detailed_comparison_response(prompt):
    """Generate a detailed comparison response using arena, nexa, tata commercial, and truck data"""
    prompt_lower = prompt.lower()
    # Arena cars
    arena_cars = ['alto k10', 'alto', 'swift', 'brezza', 'wagon-r', 'dzire', 'celerio', 's-presso', 'ertiga', 'eeco']
    # NEXA cars
    nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6']
    # Tata Commercial vehicles (legacy)
    tata_commercials = ['Small Trucks', 'Trucks']
    # Get all truck names from TRUCKS folder
    truck_names = get_all_truck_names()

    car_names = arena_cars + nexa_cars + tata_commercials + truck_names

    # Alternative car name mappings for user input variations
    car_name_mappings = {
        'wagon r': 'wagon-r',
        's presso': 's-presso',
        'xl-6': 'xl6',
        'ace pro': 'ace-pro ev',  # Default to EV when just "ace pro" is mentioned
        'intra': 'intra v10',
        'yodha': 'yodha-cng',
        'alto': 'alto k10'
    }

    # Extract mentioned cars with improved matching logic
    mentioned_cars = []

    # Sort car names by length (longest first) to prioritize more specific matches
    sorted_car_names = sorted(car_names, key=len, reverse=True)

    for car in sorted_car_names:
        if car in prompt_lower and car not in mentioned_cars:
            # Check if this is not a substring of an already found car
            is_substring_of_existing = False
            for existing_car in mentioned_cars:
                if car in existing_car:
                    is_substring_of_existing = True
                    break

            if not is_substring_of_existing:
                mentioned_cars.append(car)
                # Stop after finding 2 cars for comparison
                if len(mentioned_cars) >= 2:
                    break

    # Check for alternative spellings if we don't have enough matches
    if len(mentioned_cars) < 2:
        for alt_name, canonical_name in car_name_mappings.items():
            if alt_name in prompt_lower and canonical_name not in mentioned_cars:
                mentioned_cars.append(canonical_name)
                if len(mentioned_cars) >= 2:
                    break

    # If still not enough matches, try partial matching for common patterns
    if len(mentioned_cars) < 2:
        # Look for patterns like "tata ace" which should match "tata ace gold petrol" etc.
        partial_patterns = [
            ('tata ace pro', ['tata ace gold petrol', 'tata ace gold diesel', 'tata ace ht+']),
            ('tata ace', ['tata ace gold petrol', 'tata ace gold diesel', 'tata ace ht+', 'tata ace ev']),
            ('ace pro', ['ace pro ev', 'ace pro petrol', 'ace pro bi fuel']),
        ]

        for pattern, candidates in partial_patterns:
            if pattern in prompt_lower:
                # Find the best candidate that's not already mentioned
                for candidate in candidates:
                    if candidate in car_names and candidate not in mentioned_cars:
                        mentioned_cars.append(candidate)
                        break
                if len(mentioned_cars) >= 2:
                    break

    # If we have exactly 2 vehicles, generate detailed comparison using unified function
    if len(mentioned_cars) >= 2:
        vehicle1_name = mentioned_cars[0]
        vehicle2_name = mentioned_cars[1]

        # Use the unified comparison function for all vehicle types
        detailed_comparison = compare_vehicles_unified(vehicle1_name, vehicle2_name)
        if detailed_comparison:
            return detailed_comparison

    return None

def get_context_aware_response(step_name, session_id=None):
    """
    Get context-aware response that can use common keys with car-specific data

    Args:
        step_name (str): The step name to get response for
        session_id (str): Session ID for context tracking

    Returns:
        dict: Response with status, message, and buttons
    """
    try:
        # Get current car context
        current_car = get_user_context(session_id)

        # Handle common keys with context
        if current_car and step_name in ["Ex-showroom Price", "Request Brochure", "More about this car"]:
            # Map to car-specific key
            car_specific_key = f"{current_car} {step_name}"
            if car_specific_key in WHATSAPP_CONFIG:
                return get_whatsapp_response(car_specific_key)

        # Fall back to regular response
        return get_whatsapp_response(step_name)

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error getting context-aware response: {str(e)}"
        }

def show_car_details_with_buttons(car_name, session_id=None):
    """
    Show car details with action buttons in WhatsApp-friendly format
    Prioritizes JSON flow over car data manager and sets user context
    """
    # Set user context when they select a car
    if session_id:
        set_user_context(session_id, car_name)

    # First check if car exists in JSON config (exact match)
    if car_name in WHATSAPP_CONFIG:
        return get_whatsapp_response(car_name)

    # Check for case-insensitive match in JSON config
    for key in WHATSAPP_CONFIG.keys():
        if key.lower() == car_name.lower():
            if session_id:
                set_user_context(session_id, key)  # Use the exact key as context
            return get_whatsapp_response(key)

    # Check for partial matches in JSON config (for car names)
    car_name_lower = car_name.lower()
    for key in WHATSAPP_CONFIG.keys():
        if car_name_lower in key.lower() or key.lower() in car_name_lower:
            # Additional check to ensure it's likely a car name, not a generic action
            if any(car_word in key.lower() for car_word in ['alto','swift', 'baleno', 'dzire', 'ertiga', 'brezza', 'xl6', 'ciaz', 'ignis', 'fronx', 'wagon']):
                if session_id:
                    set_user_context(session_id, key)  # Use the exact key as context
                return get_whatsapp_response(key)

    # Get car data from car data manager as fallback
    car_data = get_car_data(car_name)

    if car_data:
        # Use car data specifications
        return format_car_details_from_data(car_data, {}, {})

    # If not found, return error
    return {
        "status": "error",
        "message": f"❌ Sorry, '{car_name}' not found in our inventory"
    }


def get_all_cars():
    """
    Get all cars from both Arena and Nexa using cached data
    """
    all_cars = []

    # Get all cars from car data manager
    all_cached_cars = car_data_manager.get_all_cars()

    # Add Arena cars
    arena_cars = all_cached_cars.get('arena', {})
    for _, car_data in arena_cars.items():
        car_copy = car_data.copy()
        car_copy['dealership_type'] = 'Arena'
        car_copy['dealership_info'] = {}
        all_cars.append(car_copy)

    # Add Nexa cars
    nexa_cars = all_cached_cars.get('nexa', {})
    for _, car_data in nexa_cars.items():
        car_copy = car_data.copy()
        car_copy['dealership_type'] = 'Nexa'
        car_copy['dealership_info'] = {}
        all_cars.append(car_copy)

    return {
        "status": "success",
        "cars": all_cars,
        "total_found": len(all_cars),  # Changed from total_count to total_found for format_search_results_message
        "total_count": len(all_cars),
        "arena_count": len(arena_cars),
        "nexa_count": len(nexa_cars)
    }

def handle_truck_query(query, search_criteria):
    """
    Handle truck/commercial vehicle queries using proper truck database
    """
    # Import truck data from Trucks folder
    try:
        from database.truck import trucks_data
        from database.small_trucks import small_trucks_data

    except ImportError:
        logger.error("Failed to import truck data")
        trucks_data = {}

    query_lower = query.lower()
    truck_vehicles = []

    # Determine what type of trucks to show based on query
    if 'lorry' in query_lower or 'heavy truck' in query_lower:
        # Show heavy-duty trucks and lorries (exclude vans)
        preferred_categories = ['Heavy Duty Truck', 'Heavy Commercial Vehicle', 'Tractor Trailer']
    elif 'small truck' in query_lower or 'mini truck' in query_lower:
        # Show small trucks (exclude heavy trucks and vans)
        preferred_categories = ['Light Commercial Vehicle', 'Medium Commercial Vehicle']
    else:
        # General truck query - show a mix but prioritize actual trucks over vans
        preferred_categories = ['Heavy Duty Truck', 'Heavy Commercial Vehicle', 'Medium Commercial Vehicle', 'Light Commercial Vehicle']

    # Filter trucks based on category and exclude vans for lorry queries
    for truck_data in trucks_data.values():
        category = truck_data.get('category', '')
        name = truck_data.get('name', '')

        # Skip vans for lorry queries
        if 'lorry' in query_lower and ('Van' in category or 'Winger' in name):
            continue

        # Include trucks that match preferred categories
        if category in preferred_categories:
            enhanced_truck = {
                'name': truck_data.get('name', ''),
                'category': category,
                'dealership_type': 'Tata Commercial',
                'fuel_types': truck_data.get('fuel_types', ['Diesel']),
                'specifications': truck_data.get('specifications', {}),
                'dealership_info': {}
            }
            truck_vehicles.append(enhanced_truck)

    # Limit results for WhatsApp compatibility and sort by GVW
    truck_vehicles = sorted(truck_vehicles, key=lambda x: x.get('specifications', {}).get('gvw', '0'), reverse=True)
    truck_vehicles = truck_vehicles[:10]  # Limit to 10 for WhatsApp

    # Apply budget filtering if specified
    budget = search_criteria.get('price_range', {}).get('max')
    if budget:
        # Simple budget filtering (can be enhanced with actual truck prices)
        if budget < 10:
            truck_vehicles = [v for v in truck_vehicles if 'Mini' in v['category'] or 'Small' in v['category']]
        elif budget < 20:
            truck_vehicles = [v for v in truck_vehicles if 'Medium' not in v['category'] or 'Heavy' not in v['category']]

    return {
        "status": "success",
        "cars": truck_vehicles,  # Using 'cars' key for consistency
        "total_found": len(truck_vehicles),
        "query": query,
        "search_type": "truck_search",
        "criteria": search_criteria
    }

def search_cars(query):
    """
    Intelligent car search using AI to understand natural language queries
    Handles both cars and trucks based on query intent
    """
    # Use AI to understand the search intent and extract criteria
    search_criteria = extract_search_criteria_with_ai(query)

    # Check if this is a truck/commercial vehicle query
    if search_criteria.get("vehicle_type") == "truck":
        return handle_truck_query(query, search_criteria)

    # Get all available cars
    all_cached_cars = car_data_manager.get_all_cars()
    matching_cars = []

    # Search through all cars using AI-extracted criteria
    for category in ['arena', 'nexa']:
        for _, car_data in all_cached_cars.get(category, {}).items():
            if car_matches_criteria(car_data, search_criteria, category):
                enhanced_car = {
                    'name': car_data.get('name', ''),
                    'category': car_data.get('category', ''),
                    'dealership_type': category.title(),
                    'fuel_types': car_data.get('fuel_types', []),
                    'specifications': car_data,
                    'dealership_info': {}
                }
                matching_cars.append(enhanced_car)

    return {
        "status": "success",
        "cars": matching_cars,
        "total_found": len(matching_cars),
        "query": query,
        "search_type": "ai_powered_search",
        "criteria": search_criteria
    }

def extract_search_criteria_with_ai(query):
    """
    Use AI to extract search criteria from natural language query
    """
    try:
        if not model:
            # Fallback to simple extraction if AI not available
            return extract_search_criteria_fallback(query)

        # Create a focused prompt for search criteria extraction
        extraction_prompt = f"""
Analyze this car search query and extract the search criteria in JSON format.

Query: "{query}"

Extract the following criteria if mentioned:
- car_names: List of specific car names mentioned
- fuel_types: List of fuel types (petrol, diesel, cng, hybrid, electric)
- price_range: {{min: number, max: number}} in lakhs (convert any price mentions)
- categories: List of categories (hatchback, sedan, suv, mpv, premium, budget, entry-level)
- features: List of specific features mentioned
- transmission: List of transmission types (manual, automatic, amt, cvt)
- dealership: arena or nexa if specifically mentioned

Return only valid JSON format:
{{
  "car_names": [],
  "fuel_types": [],
  "price_range": {{"min": null, "max": null}},
  "categories": [],
  "features": [],
  "transmission": [],
  "dealership": null,
  "general_intent": "brief description of what user wants"
}}
"""

        # Use a simple chat session for extraction
        chat = model.start_chat()
        response = chat.send_message(extraction_prompt)

        if response.candidates and response.candidates[0].content.parts:
            ai_response = response.candidates[0].content.parts[0].text

            # Try to parse JSON from AI response
            import json
            try:
                # Extract JSON from response (in case AI adds extra text)
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = ai_response[json_start:json_end]
                    criteria = json.loads(json_str)
                    # Post-process AI results for better user experience
                    criteria = post_process_ai_criteria(criteria, query)
                    return criteria
            except json.JSONDecodeError:
                logger.warning(f"Could not parse AI response as JSON: {ai_response}")

    except Exception as e:
        error_msg = str(e)
        if "429" in error_msg or "quota" in error_msg.lower():
            logger.warning(f"AI extraction rate limited: {e}")
            # Add small delay for rate limiting
            time.sleep(1)
        else:
            logger.warning(f"AI extraction failed: {e}")

    # Fallback to simple extraction
    return extract_search_criteria_fallback(query)

def post_process_ai_criteria(criteria, query):
    """
    Post-process AI-extracted criteria to make them more user-friendly
    """
    query_lower = query.lower()
    price_range = criteria.get("price_range", {})

    # Handle "starting from" queries - add reasonable upper bound
    if (price_range.get("min") and not price_range.get("max") and
        ('from starting' in query_lower or 'starting from' in query_lower or
         ('starting' in query_lower and 'lakh' in query_lower))):

        base_price = price_range["min"]
        # Set reasonable upper bound (2.5x the starting price)
        criteria["price_range"]["max"] = base_price * 2.5
        logger.info(f"Added upper bound for 'starting from' query: {base_price} -> {base_price * 2.5}")

    return criteria

def extract_search_criteria_fallback(query):
    """
    Fallback method for extracting search criteria without AI
    """
    query_lower = query.lower()
    criteria = {
        "car_names": [],
        "fuel_types": [],
        "price_range": {"min": None, "max": None},
        "categories": [],
        "features": [],
        "transmission": [],
        "dealership": None,
        "general_intent": query
    }

    # Extract budget information
    import re
    budget_patterns = [
        r'(\d+)\s*l(?:akh)?',  # "10L" or "10 lakh"
        r'(\d+)\s*lakh',       # "10 lakh"
        r'budget.*?(\d+)',     # "budget 10"
        r'have.*?(\d+)',       # "I have 10"
    ]

    for pattern in budget_patterns:
        match = re.search(pattern, query_lower)
        if match:
            budget = float(match.group(1))
            criteria["price_range"]["max"] = budget
            break

    # Extract vehicle categories with better detection
    if 'suv' in query_lower:
        criteria["categories"].append("SUV")
    if 'hatchback' in query_lower:
        criteria["categories"].append("Hatchback")
    if 'sedan' in query_lower:
        criteria["categories"].append("Sedan")

    # Handle truck/commercial vehicle queries
    truck_keywords = ['truck', 'pickup', 'commercial', 'goods carrier', 'cargo', 'mini truck', 'small truck', 'lorry', 'heavy truck']
    if any(keyword in query_lower for keyword in truck_keywords):
        criteria["vehicle_type"] = "truck"

    # Handle specific vehicle types
    if 'pickup' in query_lower:
        criteria["categories"].append("Pickup")
    if 'family car' in query_lower:
        criteria["categories"].extend(["SUV", "Sedan", "MUV"])  # Family-friendly categories

    # Enhanced price extraction with user-friendly logic
    price_numbers = re.findall(r'(\d+)l?', query_lower)
    if len(price_numbers) >= 2 and ('to' in query_lower or '-' in query_lower):
        criteria["price_range"]["min"] = float(price_numbers[0])
        criteria["price_range"]["max"] = float(price_numbers[1])
    elif price_numbers and ('under' in query_lower or 'below' in query_lower):
        criteria["price_range"]["max"] = float(price_numbers[0])
    elif price_numbers and ('above' in query_lower or 'over' in query_lower):
        criteria["price_range"]["min"] = float(price_numbers[0])
    elif price_numbers and ('from starting' in query_lower or 'starting from' in query_lower or 'starting' in query_lower):
        # For "starting from X", set a reasonable upper bound to avoid showing premium cars
        base_price = float(price_numbers[0])
        criteria["price_range"]["min"] = base_price
        criteria["price_range"]["max"] = base_price * 2.5  # Reasonable upper bound

    # Simple fuel type detection
    if 'petrol' in query_lower:
        criteria["fuel_types"].append('petrol')
    if 'diesel' in query_lower:
        criteria["fuel_types"].append('diesel')
    if 'cng' in query_lower:
        criteria["fuel_types"].append('cng')
    if 'hybrid' in query_lower:
        criteria["fuel_types"].append('hybrid')

    # Enhanced car name detection using new system
    # Extract car names using CAR_NAME_MAPPINGS
    for mapping, car_name in CAR_NAME_MAPPINGS.items():
        if mapping in query_lower and car_name not in criteria["car_names"]:
            criteria["car_names"].append(car_name)

    # Also check direct car names from registry
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in query_lower:
            if car_name not in criteria["car_names"]:
                criteria["car_names"].append(car_name)

    return criteria

def car_matches_criteria(car_data, criteria, category):
    """
    Check if a car matches the extracted search criteria
    """
    # If specific car names are mentioned, check for exact match
    if criteria.get("car_names"):
        car_name = car_data.get('name', '').lower()
        for search_name in criteria["car_names"]:
            if search_name.lower() in car_name or car_name in search_name.lower():
                return True
        return False  # If specific names mentioned but no match, exclude

    # Check categories (SUV, hatchback, sedan, etc.) - STRICT FILTERING
    if criteria.get("categories"):
        car_category = car_data.get('category', '').lower()
        category_match = False
        for search_category in criteria["categories"]:
            search_cat_lower = search_category.lower()
            # Handle SUV matching (includes Compact SUV, Mid-size SUV, Micro SUV)
            if search_cat_lower == 'suv' and 'suv' in car_category:
                category_match = True
                break
            # Handle hatchback matching
            elif search_cat_lower == 'hatchback' and 'hatchback' in car_category:
                category_match = True
                break
            # Handle sedan matching
            elif search_cat_lower == 'sedan' and 'sedan' in car_category:
                category_match = True
                break
            # Generic category matching
            elif search_cat_lower in car_category or car_category in search_cat_lower:
                category_match = True
                break
        if not category_match:
            return False  # No category match found - STRICT filtering

    # Check fuel types
    if criteria.get("fuel_types"):
        car_fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
        for search_fuel in criteria["fuel_types"]:
            search_fuel_lower = search_fuel.lower()
            # Handle hybrid matching
            if search_fuel_lower == 'hybrid' or search_fuel_lower == 'petrol':
                if any('hybrid' in ft or 'petrol' in ft for ft in car_fuel_types):
                    break
            elif any(search_fuel_lower in ft for ft in car_fuel_types):
                break
        else:
            return False  # No fuel type match found

    # Check price range
    price_range = criteria.get("price_range", {})
    if price_range.get("min") or price_range.get("max"):
        # Try to find variants in the new data structure
        variants = car_data.get('variants', [])

        # If no variants found, try the new format with car name + " variants"
        if not variants:
            car_name = car_data.get('name', '')
            # Try different variant key formats
            for key in car_data.keys():
                if key.endswith(' variants'):
                    variants = car_data[key]
                    break

        price_match = False
        for variant in variants:
            # Handle both 'price' and 'price_range' fields
            price_str = variant.get('price', '') or variant.get('price_range', '')
            price_numbers = re.findall(r'[\d.]+', price_str)
            if price_numbers:
                car_price = float(price_numbers[0])
                min_price = price_range.get("min")
                max_price = price_range.get("max")

                # Check if car price matches the criteria
                matches_min = not min_price or car_price >= min_price
                matches_max = not max_price or car_price <= max_price

                if matches_min and matches_max:
                    price_match = True
                    break

        if not price_match:
            return False

    # Check dealership preference
    if criteria.get("dealership"):
        if criteria["dealership"].lower() != category.lower():
            return False

    # If no specific criteria matched but no exclusions either, include the car
    return True

def is_specific_car_query(query):
    """
    Check if the query is asking for a specific car name vs. general search criteria
    Uses the new CAR_DATA_REGISTRY and CAR_NAME_MAPPINGS
    """
    query_lower = query.lower()

    # Check against car registry
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in query_lower:
            return True

    # Check against name mappings
    for mapping in CAR_NAME_MAPPINGS.keys():
        if mapping in query_lower:
            return True

    # If query contains general search terms, it's not specific
    general_terms = [
        'cars', 'petrol', 'diesel', 'hybrid', 'cng', 'automatic', 'manual',
        'under', 'above', 'below', 'lakhs', 'budget', 'premium', 'fuel',
        'mileage', 'price', 'range', 'show me', 'find', 'search'
    ]

    # If query is mostly general terms, it's not specific
    general_word_count = sum(1 for term in general_terms if term in query_lower)
    total_words = len(query_lower.split())

    # If more than 50% of words are general terms, treat as general search
    if total_words > 0 and (general_word_count / total_words) > 0.5:
        return False

    return False  # Default to general search for safety


# ============================================================================
# META FORM WEBHOOK ENDPOINTS
# ============================================================================

def verify_meta_webhook_signature(payload, signature):
    """
    Verify Meta webhook signature for security

    Args:
        payload (bytes): Raw request payload
        signature (str): X-Hub-Signature-256 header value

    Returns:
        bool: True if signature is valid
    """
    try:
        app_secret = os.getenv('META_APP_SECRET')
        if not app_secret:
            logger.warning("⚠️ META_APP_SECRET not configured")
            return True  # Allow requests if no secret configured (for testing)

        # Remove 'sha256=' prefix if present
        if signature.startswith('sha256='):
            signature = signature[7:]

        # Calculate expected signature
        expected_signature = hmac.new(
            app_secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(expected_signature, signature)

    except Exception as e:
        logger.error(f"❌ Error verifying webhook signature: {e}")
        return False

def identify_form_type(form_data):
    """
    Identify the type of Meta form based on form data

    Args:
        form_data (dict): Form data from Meta webhook

    Returns:
        str: Form type identifier
    """
    # Check for insurance form indicators
    insurance_indicators = [
        'insurance', 'book_insurance', 'Book_a_Insurance', 'book_a_insurance',
        'policy', 'premium', 'coverage', 'claim', 'motor_insurance',
        'vehicle_insurance', 'car_insurance', 'insurance_type'
    ]

    # Check for service form indicators
    service_indicators = [
        'service', 'book_service', 'Book_a_Service', 'book_a_service', 'maintenance', 'repair',
        'servicing', 'car_service', 'vehicle_service', 'service_booking',
        'service_appointment', 'oil_change', 'brake_service', 'tune_up', 'service_type'
    ]

    # Convert form data to lowercase string for checking
    form_str = json.dumps(form_data).lower()

    # Check for insurance form first
    if any(indicator.lower() in form_str for indicator in insurance_indicators):
        return 'insurance_form'

    # Check for service form
    if any(indicator.lower() in form_str for indicator in service_indicators):
        return 'service_form'

    # Check specific field names
    if any(field in form_data for field in ['insurance_type', 'policy_number', 'coverage_type', 'current_insurer']):
        return 'insurance_form'
    elif any(field in form_data for field in ['service_type', 'service_date', 'car_registration', 'car_model']):
        return 'service_form'

    # Default to service form
    return 'service_form'

def normalize_field_name(field_name):
    """
    Normalize field names for consistent access

    Args:
        field_name (str): Original field name from form

    Returns:
        str: Normalized field name
    """
    if not field_name:
        return field_name

    # Convert to lowercase and replace spaces with underscores
    normalized = field_name.lower().replace(' ', '_').replace('-', '_')

    # Handle specific field name mappings
    field_mappings = {
        # Alternate number variations
        'alternate_number': 'alternate_number',
        'alternative_number': 'alternate_number',
        'alt_number': 'alternate_number',
        'alternate_phone': 'alternate_number',
        'alternative_phone': 'alternate_number',
        'second_number': 'alternate_number',
        'secondary_number': 'alternate_number',

        # Mobile number variations (be careful with 'phone' - handle separately)
        'mobile_number': 'mobile_number',
        'phone_number': 'mobile_number',
        'mobile': 'mobile_number',
        'primary_phone': 'mobile_number',
        'primary_number': 'mobile_number',

        # Vehicle number variations
        'vehicle_number': 'vehicle_number',
        'registration_number': 'vehicle_number',
        'reg_number': 'vehicle_number',
        'car_number': 'vehicle_number',
        'vehicle_reg': 'vehicle_number',

        # Name variations
        'full_name': 'name',
        'customer_name': 'name',
        'user_name': 'name',
        'client_name': 'name'
    }

    return field_mappings.get(normalized, normalized)


def extract_form_data(webhook_data):
    """
    Extract form data from Meta webhook payload

    Args:
        webhook_data (dict): Webhook payload from Meta

    Returns:
        dict: Extracted form data
    """
    try:
        form_data = {}

        # Handle different webhook structures
        if 'entry' in webhook_data:
            for entry in webhook_data['entry']:
                if 'changes' in entry:
                    for change in entry['changes']:
                        if change.get('field') == 'leadgen':
                            # Lead generation form
                            leadgen_data = change.get('value', {})
                            if 'leadgen_id' in leadgen_data:
                                form_data['leadgen_id'] = leadgen_data['leadgen_id']
                            if 'form_id' in leadgen_data:
                                form_data['form_id'] = leadgen_data['form_id']
                            if 'field_data' in leadgen_data:
                                for field in leadgen_data['field_data']:
                                    field_name = field.get('name', '').lower()
                                    field_value = field.get('values', [''])[0] if field.get('values') else ''

                                    # Normalize field names for consistent access
                                    normalized_field_name = normalize_field_name(field_name)
                                    form_data[normalized_field_name] = field_value

                                    # Also store with original name for backward compatibility
                                    form_data[field_name] = field_value

        # Handle direct form data (for testing or different webhook formats)
        elif 'form_data' in webhook_data:
            form_data.update(webhook_data['form_data'])

        # Handle flat structure
        else:
            form_data.update(webhook_data)

        # Add timestamp
        form_data['submission_timestamp'] = datetime.now().isoformat()

        return form_data

    except Exception as e:
        logger.error(f"❌ Error extracting form data: {e}")
        return {}

@app.route('/webhook/meta', methods=['GET', 'POST'])
def meta_webhook():
    """
    Meta webhook endpoint for form submissions
    Handles both verification (GET) and form data (POST)
    """
    if request.method == 'GET':
        # Webhook verification
        verify_token = os.getenv('META_VERIFY_TOKEN', 'your_verify_token')

        mode = request.args.get('hub.mode')
        token = request.args.get('hub.verify_token')
        challenge = request.args.get('hub.challenge')

        if mode == 'subscribe' and token == verify_token:
            logger.info("✅ Meta webhook verified successfully")
            return challenge
        else:
            logger.warning("⚠️ Meta webhook verification failed")
            return abort(403)

    elif request.method == 'POST':
        # Handle form submission
        try:
            # Get raw payload for signature verification
            payload = request.get_data()
            signature = request.headers.get('X-Hub-Signature-256', '')

            # Verify signature (optional, for security)
            if not verify_meta_webhook_signature(payload, signature):
                logger.warning("⚠️ Invalid webhook signature")
                # Uncomment the next line to enforce signature verification
                # return abort(403)

            # Parse JSON data
            webhook_data = request.get_json()
            if not webhook_data:
                logger.error("❌ No JSON data received")
                return jsonify({'error': 'No data received'}), 400

            logger.info(f"📨 Received Meta webhook data: {json.dumps(webhook_data, indent=2)}")

            # Extract form data
            form_data = extract_form_data(webhook_data)
            if not form_data:
                logger.error("❌ Could not extract form data")
                return jsonify({'error': 'Could not extract form data'}), 400

            # Identify form type
            form_type = identify_form_type(form_data)
            logger.info(f"🔍 Identified form type: {form_type}")

            # Store in Google Sheets
            success = store_meta_form_data(form_data, form_type)

            if success:
                logger.info(f"✅ Successfully stored {form_type} data in Google Sheets")
                return jsonify({
                    'status': 'success',
                    'message': 'Form data stored successfully',
                    'form_type': form_type
                }), 200
            else:
                logger.error(f"❌ Failed to store {form_type} data")
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to store form data'
                }), 500

        except Exception as e:
            logger.error(f"❌ Error processing Meta webhook: {e}")
            return jsonify({'error': str(e)}), 500

    return jsonify({'error': 'Method not allowed'}), 405

@app.route('/generate', methods=['POST'])
def generate_text():
    global SESSION_ID, model

    # Track request start time for performance logging
    request_start_time = time.time()

    # ✅ Helper to detect greetings
    def is_greeting(prompt):
        greetings = {"hi wic", "hello", "hey", "hii", "hi!", "hey!", "start", "menu", "reset", "greet", "hi"}
        return prompt.strip().lower() in greetings

    response_data = {
        "text": "",
        "function_response_id": "",
        "function_response": [],
        "llm": ""
    }

    # Flag to track if function calls were processed
    function_calls_processed = False

    try:
        data = request.get_json(silent=True) or {}
        prompt = data.get('prompt')
        user_id = data.get('user_id')

        if not prompt or not user_id:
            return jsonify({'error': 'Missing prompt or user_id in request body'}), 400

        prompt = prompt.strip()
        function_response = ""

        # Parse user ID for database logging
        actual_user_id, session_id, project_id = parse_user_id(user_id)

        # Log the incoming user message (we'll log response later with token counts)
        try:
            # We'll log with token counts after getting the response
            pass
        except Exception as db_error:
            logger.warning(f"Database logging failed: {db_error}")
            # Continue processing even if DB logging fails

        # Initialize session if needed
        if user_id not in SESSION_ID:
            SESSION_ID[user_id] = []

        # ✅ Main menu trigger: greeting only (not new user)
        if is_greeting(prompt):
            SESSION_ID[user_id] = []  # reset session
            greeting_response = get_whatsapp_response("Main Menu")
            response_data["text"] = greeting_response["message"]
            response_data["llm"] = greeting_response["message"]
            response_data["function_response_id"] = 1
            response_data["function_response"] = greeting_response.get("buttons", [])

            # Add media_id if present in greeting response
            if "media_id" in greeting_response:
                response_data["media_id"] = greeting_response["media_id"]

        else:
            logger.info(f"🔍 Processing non-greeting prompt: '{prompt}'")

            # ✅ Check for form submission (JSON data with flow_token or form fields)
            try:
                import json
                raw_form_data = json.loads(prompt)
                if isinstance(raw_form_data, dict) and ('flow_token' in raw_form_data or
                    any(field in raw_form_data for field in ['name', 'mobile_number', 'email', 'service_type'])):
                    logger.info(f"🔍 Detected form submission: {raw_form_data}")

                    # Normalize form data field names for consistent processing
                    form_data = {}

                    # First pass: copy all original data
                    for key, value in raw_form_data.items():
                        form_data[key] = value

                    # Special handling for phone/mobile_number fields
                    if 'phone' in raw_form_data and 'mobile_number' in raw_form_data:
                        # Both phone and mobile_number exist - use phone as alternate
                        form_data['alternate_number'] = raw_form_data['phone']
                        logger.info(f"🔍 Using phone {raw_form_data['phone']} as alternate number")
                    elif 'phone' in raw_form_data and 'mobile_number' not in raw_form_data:
                        # Only phone exists - use it as mobile_number
                        form_data['mobile_number'] = raw_form_data['phone']
                        logger.info(f"🔍 Using phone {raw_form_data['phone']} as mobile number")

                    # Second pass: add other normalized keys
                    for key, value in raw_form_data.items():
                        if key == 'phone':
                            continue  # Already handled above

                        normalized_key = normalize_field_name(key)

                        # Only set normalized key if it doesn't already exist or if the original key is the same
                        if normalized_key not in form_data or key == normalized_key:
                            form_data[normalized_key] = value

                    logger.info(f"🔍 Normalized form data: {form_data}")

                    # Process form submission
                    form_type = identify_form_type(form_data)
                    logger.info(f"🔍 Form type identified: {form_type}")

                    # Store in Google Sheets
                    success = store_meta_form_data(form_data, form_type)

                    if success:
                        # Generate appropriate thank you message based on form type
                        if form_type == 'insurance_form':
                            thank_you_message = f"✅ **Insurance Application Submitted!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Alternate No: {form_data.get('alternate_number', 'N/A')}\n• Vehicle No: {form_data.get('vehicle_number', 'N/A')}\n• Insurance Type: {form_data.get('insurance_type', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n🛡️ **What's Next:**\n• Our insurance team will contact you within 24 hours\n• We'll provide you with the best insurance quotes\n• Policy will be processed as per your requirements\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing our insurance services!"
                        else:
                            # Service form
                            thank_you_message = f"✅ **Service Booking Confirmed!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Alternate No: {form_data.get('alternate_number', 'N/A')}\n• Vehicle No: {form_data.get('vehicle_number', 'N/A')}\n• Service: {form_data.get('service_type', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n🔧 **What's Next:**\n• Our team will contact you within 24 hours\n• We'll confirm your appointment details\n• Service will be scheduled at your convenience\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing our service!"

                        response_data["text"] = thank_you_message
                        response_data["llm"] = thank_you_message
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = []

                        logger.info(f"✅ Form submission processed successfully for {form_data.get('name', 'Customer')}")
                    else:
                        error_message = "❌ **Submission Failed**\n\nSorry, there was an issue processing your request. Please try again or contact us directly.\n\n📞 Call us: +91-XXXXXXXXXX"
                        response_data["text"] = error_message
                        response_data["llm"] = error_message
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = []

                        logger.error(f"❌ Form submission failed for {form_data.get('name', 'Customer')}")

                    # Return early - don't process as regular query
                    return jsonify({
                        'generated_text': response_data["text"],
                        'function_response_id': response_data["function_response_id"],
                        'function_response': response_data["function_response"]
                    })

            except (json.JSONDecodeError, TypeError):
                # Not a JSON form submission, continue with regular processing
                pass

            # Check for comparison queries FIRST (highest priority)
            if is_comparison_query(prompt):
                # Try to generate detailed comparison using arena data
                detailed_comparison = generate_detailed_comparison_response(prompt)
                if detailed_comparison:
                    response_data["text"] = detailed_comparison
                    response_data["llm"] = detailed_comparison

                    # Add comparison buttons
                    comparison_buttons = generate_comparison_buttons(prompt)
                    if comparison_buttons:
                        response_data["function_response"] = comparison_buttons
                        response_data["function_response_id"] = 1
                else:
                    response_data["text"] = "❌ Sorry, I couldn't generate a comparison for those cars. Please try with different car names."
                    response_data["llm"] = response_data["text"]

            # Check if it's a car-specific action request (format: "CarName|Action")
            elif "|" in prompt:
                logger.info(f"🔧 Detected car-specific action format: '{prompt}'")
                parts = prompt.split("|", 1)
                if len(parts) == 2:
                    car_name = parts[0].strip()
                    action = parts[1].strip()

                    logger.info(f"🚗 Car-specific action: {car_name} -> {action}")

                    # Try to handle car-specific action using new car data system (NO NAMESPACING)
                    car_response = get_car_response_with_media_id(car_name, action, user_id)

                    if car_response:
                        response_data["text"] = car_response["message"]
                        response_data["llm"] = car_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = car_response.get("buttons", [])

                        # Add media_id if present
                        if "media_id" in car_response:
                            response_data["media_id"] = car_response["media_id"]

                        logger.info(f"✅ Found car action using new system: {car_name} -> {action}")
                    else:
                        # Fall through to regular flow if car-specific action fails
                        whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                        if whatsapp_response:
                            response_data["text"] = whatsapp_response["message"]
                            response_data["llm"] = whatsapp_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["function_response"] = whatsapp_response.get("buttons", [])
                            # Add media_id if present in whatsapp response
                            if "media_id" in whatsapp_response:
                                response_data["media_id"] = whatsapp_response["media_id"]
                else:
                    # Invalid format, fall through to regular flow
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        response_data["text"] = whatsapp_response["message"]
                        response_data["llm"] = whatsapp_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = whatsapp_response.get("buttons", [])
                        # Add media_id if present in whatsapp response
                        if "media_id" in whatsapp_response:
                            response_data["media_id"] = whatsapp_response["media_id"]
            else:
                logger.info(f"🔍 Processing regular prompt (no | format): '{prompt}'")

                # Check for capacity-based queries FIRST (highest priority)
                prompt_lower = prompt.lower()
                required_capacity = parse_weight_requirement(prompt_lower)
                if required_capacity and any(word in prompt_lower for word in ['truck', 'lorry', 'vehicle', 'capacity', 'payload', 'load']):
                    logger.info(f"🚛 Capacity-based query detected: {required_capacity}kg")
                    matching_vehicles = search_vehicles_by_capacity(required_capacity)

                    if matching_vehicles:
                        # Create response with explanation and vehicle buttons
                        explanation = f"I understand you're looking for a vehicle with {required_capacity:,}kg capacity.\n\n"
                        explanation += f"Vehicles in this capacity range are typically designed for heavy-duty transportation and substantial loads. "
                        explanation += f"I've found {len(matching_vehicles)} vehicles that meet or exceed your {required_capacity:,}kg requirement.\n\n"
                        explanation += "These vehicles come with powerful engines, robust chassis, and are suitable for various commercial applications.\n\n"
                        explanation += "📋 Here are vehicles that match your capacity requirement:"

                        # Create vehicle buttons
                        vehicle_buttons = [vehicle['name'] for vehicle in matching_vehicles[:10]]

                        response_data["text"] = explanation
                        response_data["llm"] = explanation
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = [{
                            "data": vehicle_buttons,
                            "data_type": "list",
                            "message": explanation
                        }]

                        logger.info(f"✅ Found {len(matching_vehicles)} vehicles for {required_capacity}kg capacity")
                    else:
                        logger.warning(f"❌ No vehicles found for {required_capacity}kg capacity")

                # Check for service-related queries SECOND (high priority)
                elif any(pattern in prompt_lower for pattern in ['i want to book a service', 'i need a service', 'i want service', 'need service']):
                    logger.info(f"🔧 Service query detected: '{prompt}' - routing to Book a Service flow")
                    base_response = get_whatsapp_response("Book a Service", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Service flow not found, falling back to LLM")

                # Check for test drive queries
                elif any(pattern in prompt_lower for pattern in ['i want to book a test drive', 'i want test drive', 'i want to test drive']):
                    logger.info(f"🚗 Test drive query detected: '{prompt}' - routing to Book a Test Drive flow")
                    base_response = get_whatsapp_response("Book a Test Drive", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Test drive flow not found, falling back to LLM")

                # Check for call back queries
                elif any(pattern in prompt_lower for pattern in ['i want a call back', 'i need a call back', 'i want call back']):
                    logger.info(f"📞 Call back query detected: '{prompt}' - routing to Request a Call Back flow")
                    base_response = get_whatsapp_response("Request a Call Back", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Call back flow not found, falling back to LLM")

                # Check for image queries (car model + images/interior/exterior)
                elif any(img_word in prompt_lower for img_word in ['image', 'images', 'interior', 'exterior', 'photo', 'photos', 'picture', 'pictures']):
                    # Extract car name from the query
                    car_name = None
                    for word in prompt_lower.split():
                        # Check if word matches any car name (simplified check)
                        if word in ['brezza', 'swift', 'dzire', 'baleno', 'ertiga', 'ciaz', 'fronx', 'grand_vitara', 'xl6', 'jimny', 'ignis', 'alto', 'wagonr', 'celerio', 'eeco', 's-presso']:
                            car_name = word
                            break

                    # If no car name found in prompt, use user's car context
                    if not car_name and user_id:
                        current_car = get_user_context(user_id)
                        if current_car:
                            car_name = current_car
                            logger.info(f"📸 Using car context for image query: '{prompt}' -> {car_name}")

                    if car_name:
                        logger.info(f"📸 Image query detected for {car_name}: '{prompt}' - routing to car WhatsApp flow")

                        # Determine the specific action based on query
                        if 'interior' in prompt_lower:
                            action = 'Interior'
                        elif 'exterior' in prompt_lower:
                            action = 'Exterior'
                        else:
                            action = 'Gallery'  # Default to gallery for general image requests

                        # Try to get car response with specific action
                        car_response = get_car_response_with_media_id(car_name, action, user_id)
                        if car_response:
                            response_data["text"] = car_response["message"]
                            response_data["llm"] = car_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["function_response"] = car_response.get("buttons", [])
                            if "media_id" in car_response:
                                response_data["media_id"] = car_response["media_id"]
                            logger.info(f"✅ Found {action} images for {car_name}")
                        else:
                            # Fallback to general car flow
                            car_response = get_car_response_with_media_id(car_name, None, user_id)
                            if car_response:
                                response_data["text"] = car_response["message"]
                                response_data["llm"] = car_response["message"]
                                response_data["function_response_id"] = 1
                                response_data["function_response"] = car_response.get("buttons", [])
                                if "media_id" in car_response:
                                    response_data["media_id"] = car_response["media_id"]
                                logger.info(f"✅ Found general car flow for {car_name}")
                            else:
                                logger.warning(f"❌ No car flow found for {car_name}, falling back to LLM")
                    else:
                        logger.info(f"📸 Image query detected but no car name found and no user context: '{prompt}'")
                        # Continue to other handlers

                # Check for comparison queries (before WhatsApp flow matching)
                elif is_comparison_query(prompt):
                    # Try to generate detailed comparison using arena data
                    detailed_comparison = generate_detailed_comparison_response(prompt)
                    if detailed_comparison:
                        logger.info("✅ Generated detailed comparison successfully")
                        response_data["text"] = detailed_comparison
                        response_data["llm"] = detailed_comparison

                        # Add comparison buttons
                        comparison_buttons = generate_comparison_buttons(prompt)
                        if comparison_buttons:
                            response_data["function_response"] = comparison_buttons
                            response_data["function_response_id"] = 1
                    else:
                        logger.warning("❌ Detailed comparison failed, falling back to LLM")
                        # If detailed comparison fails, fall back to LLM
                        if model is None:
                            response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                            response_data["llm"] = response_data["text"]
                        else:
                            chat_session = model.start_chat(history=SESSION_ID[user_id])
                            response = chat_session.send_message(prompt)

                            if response.candidates and response.candidates[0].content.parts:
                                part = response.candidates[0].content.parts[0]
                                response_data["text"] = part.text
                                response_data["llm"] = part.text
                            else:
                                response_data["text"] = "❌ AI could not generate a response. Please try again."
                                response_data["llm"] = response_data["text"]

                        # Still add comparison buttons even if detailed comparison fails
                        comparison_buttons = generate_comparison_buttons(prompt)
                        if comparison_buttons:
                            response_data["function_response"] = comparison_buttons
                            response_data["function_response_id"] = 1
                else:
                    # Check if the prompt matches any WhatsApp flow step directly
                    logger.info(f"🔍 Checking WhatsApp flow match for: '{prompt}'")
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        logger.info(f"✅ Found WhatsApp flow match for: '{prompt}'")

                        # Enhance WhatsApp responses with explanatory text if missing
                        whatsapp_message = whatsapp_response.get("message", "")

                        # Add explanatory text for common button flows
                        if not whatsapp_message.strip():
                            if prompt.lower() == "main menu":
                                whatsapp_message = "🏠 **Welcome to Bhandari Automobiles!**\n\nWe are your trusted Maruti Suzuki and Tata Commercial vehicle dealer in Kolkata. Choose from the options below:"
                            elif "arena" in prompt.lower():
                                whatsapp_message = "🏟️ **Arena Cars - Affordable & Reliable**\n\nExplore our Arena range of budget-friendly cars perfect for everyday use:"
                            elif "nexa" in prompt.lower():
                                whatsapp_message = "✨ **NEXA Cars - Premium & Sophisticated**\n\nDiscover our premium NEXA collection with advanced features:"
                            elif "tata" in prompt.lower() and "commercial" in prompt.lower():
                                whatsapp_message = "🚛 **Tata Commercial Vehicles**\n\nPowerful commercial vehicles for your business needs:"

                        response_data["text"] = whatsapp_message
                        response_data["llm"] = whatsapp_message
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = whatsapp_response.get("buttons", [])
                    else:
                        logger.info(f"❌ No WhatsApp flow match found for: '{prompt}'")

                        # PRIORITY 1: Check for budget queries first (before universal vehicle handler)
                        prompt_lower = prompt.lower()

                        # Check for explicit budget keywords
                        explicit_budget = any(phrase in prompt_lower for phrase in ['under', 'from', 'budget', 'starting', 'below', 'above', 'between', 'within', 'maximum', 'minimum']) and any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l'])

                        # Check for implicit budget patterns like "i have 10l i want suv"
                        implicit_budget = any(phrase in prompt_lower for phrase in ['i have', 'my budget is', 'budget of']) and any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l']) and any(vehicle_word in prompt_lower for vehicle_word in ['car', 'suv', 'truck', 'van', 'bus', 'vehicle'])

                        if explicit_budget or implicit_budget:
                            budget_result = handle_universal_budget_query(prompt, user_id)
                            if budget_result:
                                budget_type = "Explicit" if explicit_budget else "Implicit"
                                logger.info(f"✅ {budget_type} budget query match: '{prompt}'")
                                response_data["text"] = budget_result["message"]
                                response_data["llm"] = budget_result["message"]
                                response_data["function_response_id"] = 1
                                response_data["function_response"] = budget_result.get("buttons", [])
                            else:
                                logger.warning(f"⚠️ Budget query detected but handler returned None: '{prompt}'")
                                # Fall through to universal vehicle handler
                                universal_vehicle_response = handle_universal_vehicle_query(prompt, user_id)
                                if universal_vehicle_response:
                                    logger.info(f"✅ Found universal vehicle match for: '{prompt}'")
                                    response_data["text"] = universal_vehicle_response["message"]
                                    response_data["llm"] = universal_vehicle_response["message"]
                                    response_data["function_response_id"] = 1
                                    response_data["function_response"] = universal_vehicle_response.get("buttons", [])
                        else:
                            # PRIORITY 2: Use Universal Vehicle Query Handler - works for ALL vehicle types
                            universal_vehicle_response = handle_universal_vehicle_query(prompt, user_id)
                            if universal_vehicle_response:
                                logger.info(f"✅ Found universal vehicle match for: '{prompt}'")
                                response_data["text"] = universal_vehicle_response["message"]
                                response_data["llm"] = universal_vehicle_response["message"]
                                response_data["function_response_id"] = 1
                                response_data["function_response"] = universal_vehicle_response.get("buttons", [])
                            else:
                                # No specific handlers matched - call AI model for conversational queries
                                logger.info(f"🤖 No specific handlers matched, calling AI for: '{prompt}'")

                                if model is None:
                                    response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                    response_data["llm"] = response_data["text"]
                                else:
                                    chat_session = model.start_chat(history=SESSION_ID[user_id])
                                    response = chat_session.send_message(prompt)

                                    if response.candidates and response.candidates[0].content.parts:
                                        part = response.candidates[0].content.parts[0]

                                        # Store the original AI text response first
                                        ai_text_response = part.text if part.text else ""

                                        # Set default response
                                        response_data["text"] = ai_text_response
                                        response_data["llm"] = ai_text_response

                                        # Process function calls from AI
                                        if hasattr(part, "function_call") and part.function_call:
                                            fn = part.function_call.name
                                            args = part.function_call.args
                                            logger.info(f"🔧 AI function call: {fn} with args: {args}")

                                            if fn == "get_all_cars":
                                                # Get all cars from both Arena and Nexa
                                                logger.info("🚗 Calling get_all_cars() function")
                                                result = get_all_cars()
                                                logger.info(f"🚗 get_all_cars() result: status={result.get('status')}, cars_count={len(result.get('cars', []))}")

                                                if result["status"] == "success" and result["cars"]:
                                                    logger.info("✅ get_all_cars() success - formatting response")
                                                    # Format the response with interactive buttons using the dedicated function
                                                    formatted_response = format_all_cars_message(result)
                                                    function_response = formatted_response["message"]

                                                    # WhatsApp has a 10-item limit, so show categories instead of all cars
                                                    total_cars = len(result["cars"])
                                                    if total_cars > 10:
                                                        logger.info(f"🔧 {total_cars} cars exceed WhatsApp limit (10), showing categories instead")
                                                        # Create category selection buttons (like Main Menu)
                                                        whatsapp_buttons = [{
                                                            "data": ["Arena Cars", "NEXA Cars"],
                                                            "data_type": "list",
                                                            "message": "Choose a dealership category:"
                                                        }]
                                                    else:
                                                        # Show all cars if within limit
                                                        car_names = [car.get('name', 'Unknown Car') for car in result["cars"]]
                                                        logger.info(f"🔧 Creating WhatsApp buttons for {len(car_names)} cars: {car_names[:3]}...")
                                                        whatsapp_buttons = [{
                                                            "data": car_names,
                                                            "data_type": "list",
                                                            "message": "📋 Select a car for details:"

                                                        }]
                                                    # Override the buttons from utility.py
                                                    formatted_response["buttons"] = whatsapp_buttons

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    logger.info(f"🔧 Setting response_data text to: {combined_response[:100]}...")
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for all cars to ensure WhatsApp flow works
                                                    if formatted_response.get("buttons"):
                                                        logger.info(f"🔧 Setting response_data buttons: {len(formatted_response['buttons'])} buttons")
                                                        response_data["function_response_id"] = 1
                                                        response_data["function_response"] = formatted_response["buttons"]
                                                    logger.info(f"🔧 response_data after get_all_cars: {response_data}")
                                                else:
                                                    logger.warning(f"❌ get_all_cars() failed: status={result.get('status')}, cars={len(result.get('cars', []))}")
                                                    function_response = "❌ No cars available at the moment"
                                                    response_data["text"] = function_response
                                                    response_data["llm"] = function_response

                                            elif fn == "search_cars":
                                                query = args.get("query", "")
                                                logger.info(f"🔍 AI calling search_cars with query: '{query}'")

                                                # Use AI-powered search
                                                result = search_cars(query)

                                                if result["status"] == "success" and result["cars"]:
                                                    formatted_response = utility.format_search_results_message(result)
                                                    function_response = formatted_response["message"]

                                                    # Handle WhatsApp 10-item limit for search results
                                                    if formatted_response.get("buttons"):
                                                        buttons = formatted_response["buttons"]
                                                        if buttons and len(buttons) > 0:
                                                            car_list = buttons[0].get("data", [])
                                                            if len(car_list) > 10:
                                                                logger.info(f"🔧 Search results ({len(car_list)} items) exceed WhatsApp limit, showing top 10")
                                                                # Show top 10 results
                                                                top_cars = car_list[:10]
                                                                buttons = [{
                                                                    "data": top_cars,
                                                                    "data_type": "list",
                                                                    "message": f"Top {len(top_cars)} matches (showing first 10):"
                                                                }]
                                                                formatted_response["buttons"] = buttons

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for search results to ensure WhatsApp flow works
                                                    if formatted_response.get("buttons"):
                                                        response_data["function_response_id"] = 1
                                                        response_data["function_response"] = formatted_response["buttons"]
                                                else:
                                                    # Let LLM handle no results naturally with search context
                                                    function_response = f"No cars found matching '{query}'. Search criteria: {result.get('criteria', {})}"
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response

                                            elif fn == "show_car_details":
                                                car_id = args.get("car_id", "")
                                                logger.info(f"🚗 AI calling show_car_details for: '{car_id}'")

                                                # First check if this car exists in WhatsApp flow
                                                whatsapp_car_response = check_whatsapp_flow_match(car_id, user_id)
                                                if whatsapp_car_response:
                                                    function_response = whatsapp_car_response.get("message", "")

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    response_data["function_response_id"] = 1
                                                    response_data["function_response"] = whatsapp_car_response.get("buttons", [])
                                                    function_calls_processed = True
                                                else:
                                                    # Fallback to car data manager
                                                    result = show_car_details_with_buttons(car_id, user_id)
                                                    function_response = result.get("message", "")

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for car details to ensure WhatsApp flow works
                                                    if result.get("buttons"):
                                                        response_data["function_response_id"] = 1
                                                        response_data["function_response"] = result["buttons"]

                                            else:
                                                logger.warning(f"❌ Unknown AI function call: {fn}")
                                                # Keep the AI's text response but add a note
                                                response_data["text"] = ai_text_response + "\n\n(Note: Function call not processed)" if ai_text_response else "Function call not processed"
                                                response_data["llm"] = response_data["text"]

                                        else:
                                            # No function call, just use AI text response
                                            response_data["text"] = ai_text_response
                                            response_data["llm"] = ai_text_response

                                    else:
                                        response_data["text"] = "❌ AI could not generate a response. Please try again."
                                        response_data["llm"] = response_data["text"]

                        # Check for comparison queries before calling LLM
                        if is_comparison_query(prompt):
                            logger.info(f"🔍 Detected comparison query before LLM: '{prompt}'")
                            # Try to generate detailed comparison using arena data
                            detailed_comparison = generate_detailed_comparison_response(prompt)
                            if detailed_comparison:
                                logger.info("✅ Generated detailed comparison successfully (before LLM)")
                                response_data["text"] = detailed_comparison
                                response_data["llm"] = detailed_comparison

                                # Add comparison buttons
                                comparison_buttons = generate_comparison_buttons(prompt)
                                if comparison_buttons:
                                    response_data["function_response"] = comparison_buttons
                                    response_data["function_response_id"] = 1
                            else:
                                logger.warning("❌ Detailed comparison failed, proceeding with LLM")
                                # Fall through to LLM
                                if model is None:
                                    response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                    response_data["llm"] = response_data["text"]
                                else:
                                    # Call LLM as fallback
                                    chat_session = model.start_chat(history=SESSION_ID[user_id])
                                    response = chat_session.send_message(prompt)

                                    if response.candidates and response.candidates[0].content.parts:
                                        part = response.candidates[0].content.parts[0]
                                        response_data["text"] = part.text
                                        response_data["llm"] = part.text
                                    else:
                                        response_data["text"] = "❌ AI could not generate a response. Please try again."
                                        response_data["llm"] = response_data["text"]
                        else:
                            # Use Gemini AI for all other inputs (only if function calls weren't processed)
                            if not function_calls_processed:
                                if model is None:
                                    response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                    response_data["llm"] = response_data["text"]
                                else:
                                    chat_session = model.start_chat(history=SESSION_ID[user_id])
                                    response = chat_session.send_message(prompt)

                                    # Extract token counts (similar to bitenxt_api.py)
                                    if response.usage_metadata:
                                        query_token_count = response.usage_metadata.prompt_token_count
                                        llm_token_count = response.usage_metadata.candidates_token_count
                                        total_token_count = response.usage_metadata.total_token_count
                                        logger.info(f"🔢 Token usage - Input: {query_token_count}, Output: {llm_token_count}, Total: {total_token_count}")
                                    else:
                                        query_token_count = 0
                                        llm_token_count = 0

                                if response.candidates and response.candidates[0].content.parts:
                                    part = response.candidates[0].content.parts[0]
                                    response_data["text"] = part.text
                                    response_data["llm"] = part.text
                                else:
                                    response_data["text"] = "❌ AI could not generate a response. Please try again."
                                    response_data["llm"] = response_data["text"]
                                    # Return early if no response from AI
                                    return jsonify(response_data)

                            # Continue processing function calls if we have a response
                            if response.candidates and response.candidates[0].content.parts:
                                part = response.candidates[0].content.parts[0]

                            # Check for comparison queries before processing function calls
                            if is_comparison_query(prompt):
                                logger.info(f"🔍 Detected comparison query in LLM response: '{prompt}'")
                                # Try to generate detailed comparison using arena data
                                detailed_comparison = generate_detailed_comparison_response(prompt)
                                if detailed_comparison:
                                    logger.info("✅ Generated detailed comparison successfully (overriding LLM function call)")
                                    response_data["text"] = detailed_comparison
                                    response_data["llm"] = detailed_comparison

                                    # Add comparison buttons
                                    comparison_buttons = generate_comparison_buttons(prompt)
                                    if comparison_buttons:
                                        response_data["function_response"] = comparison_buttons
                                        response_data["function_response_id"] = 1
                                else:
                                    logger.warning("❌ Detailed comparison failed, proceeding with LLM function call")

                            elif hasattr(part, "function_call") and part.function_call:
                                fn = part.function_call.name
                                args = part.function_call.args

                                if fn == "show_car_details":
                                    car_id = args.get("car_id", "")

                                    # First check if this car exists in WhatsApp flow
                                    whatsapp_car_response = check_whatsapp_flow_match(car_id, user_id)
                                    if whatsapp_car_response:
                                        function_response = whatsapp_car_response.get("message", "")
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        response_data["function_response_id"] = 1
                                        response_data["function_response"] = whatsapp_car_response.get("buttons", [])







        # Save to history
        SESSION_ID[user_id].append({"role": "user", "parts": [prompt]})
        SESSION_ID[user_id].append({"role": "model", "parts": [response_data["llm"]]})

        # Debug logging for response_data
        logger.info(f"🔍 Final response_data contents: {response_data}")

        final_response = {
            'generated_text': response_data.get("text", ""),
            'function_response_id': response_data.get("function_response_id", ""),
            'function_response': response_data.get("function_response", [])
        }

        logger.info(f"🔍 Final response being returned: {final_response}")

        # Add media_id to final response if it exists in response_data
        if "media_id" in response_data:
            final_response["media_id"] = response_data["media_id"]

        # WHATSAPP FIX: If we have both generated_text and function_response,
        # combine them so WhatsApp shows the full message
        if (final_response.get('generated_text') and
            final_response.get('function_response') and
            len(final_response['function_response']) > 0):

            # Get the generated text (main content)
            generated_text = final_response['generated_text']

            # Update the first function_response item's message to include generated_text
            if isinstance(final_response['function_response'][0], dict):
                original_message = final_response['function_response'][0].get('message', '')

                # Smart duplication detection: check if generated_text content is already present
                # Remove extra whitespace and newlines for comparison
                generated_clean = ' '.join(generated_text.split())
                original_clean = ' '.join(original_message.split()) if original_message else ''

                if original_message and generated_clean in original_clean:
                    # If generated_text content is already in original_message, just use original_message
                    combined_message = original_message
                elif original_message and len(generated_clean) > 0:
                    # Check if original message is significantly longer than generated text (indicating it might already contain it)
                    if len(original_clean) > len(generated_clean) * 1.5:
                        # Original message is much longer, likely already contains the content
                        combined_message = original_message
                    else:
                        # Safe to combine
                        combined_message = f"{generated_text}\n\n{original_message}"
                else:
                    # If no original message, just use generated_text
                    combined_message = generated_text

                # Update the message in function_response
                final_response['function_response'][0]['message'] = combined_message

        # Log conversation to database (similar to bitenxt_api.py pattern)
        try:
            response_time_ms = int((time.time() - request_start_time) * 1000)

            # Extract token counts (if available from LLM response)
            # These will be set by the LLM calls above if usage_metadata is available
            query_token_count = getattr(locals(), 'query_token_count', 0)
            llm_token_count = getattr(locals(), 'llm_token_count', 0)

            # Determine interaction type and car context
            interaction_type = "unknown"
            car_name = None

            if is_greeting(prompt):
                interaction_type = "greeting"
            elif final_response.get('function_response'):
                func_resp = final_response['function_response'][0] if final_response['function_response'] else {}
                data_type = func_resp.get('data_type', '')

                if data_type == 'button':
                    interaction_type = "button_click"
                elif data_type == 'image':
                    interaction_type = "gallery_view"
                elif data_type == 'video_btn':
                    interaction_type = "car_view"
                    # Try to extract car name from response
                    message = func_resp.get('message', '')
                    if 'Maruti Suzuki' in message:
                        import re
                        match = re.search(r'Maruti Suzuki\s+([^*]+)', message)
                        if match:
                            car_name = match.group(1).strip()
                else:
                    interaction_type = "response_view"
            else:
                interaction_type = "text_query"

            # Get current car context if not already determined
            if not car_name:
                car_name = get_user_car_context(actual_user_id)
                if car_name == 'Not specified':
                    car_name = None

            # Log the complete conversation (user query + bot response) to chat_history
            llm_response_text = final_response.get('generated_text', '')
            message_id = sync_insert_chat_message(
                actual_user_id,
                session_id,
                project_id + "-wp" if project_id else "whatsapp",
                prompt,
                query_token_count,
                llm_response_text,
                llm_token_count,
                response_time_ms,
                car_name,
                interaction_type
            )

            # Log user interaction
            interaction_data = {
                "prompt": prompt,
                "response_type": interaction_type,
                "function_response": final_response.get('function_response', []),
                "media_id": final_response.get('media_id')
            }

            sync_insert_user_interaction(
                actual_user_id,
                session_id,
                interaction_type,
                interaction_data,
                car_name
            )

            if message_id:
                logger.info(f"✅ Chat message saved with ID: {message_id}")

        except Exception as db_error:
            logger.warning(f"Database logging failed: {db_error}")

        return jsonify(final_response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8037, debug=False) 