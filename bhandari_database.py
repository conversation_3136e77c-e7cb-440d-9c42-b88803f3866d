"""
Bhandari Auto Database Module
Handles all database operations for the WhatsApp bot system
"""

import os
import asyncio
import logging
from typing import Optional, List, Dict, Tuple, Any
from datetime import datetime
import json
from contextlib import asynccontextmanager

# Try to import asyncpg, but handle gracefully if not available
try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("asyncpg not available. Database functionality will be disabled. Install with: pip install asyncpg")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global database pool
db_pool: Optional[Any] = None

# Database Configuration
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_NAME = os.getenv("DB_NAME", "bhandari_auto_db")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
DB_PORT = int(os.getenv("DB_PORT", "5432"))

# ============== Database Pool Management ==============

async def initialize_db_pool(min_size=1, max_size=10, timeout=60, command_timeout=30):
    """Initialize asyncpg connection pool."""
    global db_pool

    if not ASYNCPG_AVAILABLE:
        logger.warning("Database functionality disabled - asyncpg not available")
        return

    if db_pool is None:
        try:
            db_pool = await asyncpg.create_pool(
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME,
                host=DB_HOST,
                port=DB_PORT,
                min_size=min_size,
                max_size=max_size,
                timeout=timeout,
                command_timeout=command_timeout
            )
            logger.info("✅ Database connection pool initialized successfully.")
            await create_tables()
        except Exception as e:
            logger.error(f"❌ Error initializing database pool: {e}")
            # Don't raise - allow system to work without DB

async def close_db_pool():
    """Close asyncpg connection pool."""
    global db_pool
    if db_pool:
        await db_pool.close()
        logger.info("Database connection pool closed.")
        db_pool = None

async def create_tables():
    """Create necessary tables for the WhatsApp bot system."""
    if db_pool is None:
        return
        
    try:
        async with db_pool.acquire() as conn:
            # Chat history table (similar to bitenxt_api.py)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS chat_history (
                    message_id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255) NOT NULL,
                    project_id VARCHAR(255),
                    user_query TEXT NOT NULL,
                    query_token INTEGER,
                    llm_response TEXT,
                    llm_token INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    channel VARCHAR(50) DEFAULT 'whatsapp',
                    response_time_ms INTEGER,
                    car_context VARCHAR(255),
                    interaction_type VARCHAR(100)
                );
            """)
            
            # User interactions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS user_interactions (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    interaction_type VARCHAR(100) NOT NULL,
                    interaction_data JSONB,
                    car_name VARCHAR(255),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Service bookings table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS service_bookings (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    booking_type VARCHAR(50) NOT NULL,
                    customer_name VARCHAR(255),
                    phone_number VARCHAR(20),
                    email VARCHAR(255),
                    car_model VARCHAR(255),
                    preferred_date DATE,
                    preferred_time TIME,
                    message TEXT,
                    status VARCHAR(50) DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Live chat sessions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS live_chat_sessions (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    agent_id VARCHAR(255),
                    status VARCHAR(50) DEFAULT 'waiting',
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ended_at TIMESTAMP,
                    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                    feedback TEXT
                );
            """)
            
            # Create indexes for better performance
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_session ON chat_history(user_id, session_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_timestamp ON chat_history(timestamp);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_interactions_user_id ON user_interactions(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_interactions_car_name ON user_interactions(car_name);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_service_bookings_user_id ON service_bookings(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_live_chat_user_id ON live_chat_sessions(user_id);")
            
            logger.info("✅ Database tables created/verified successfully.")
            
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")

# ============== Helper Functions ==============

def parse_user_id(raw_user_id: str) -> Tuple[str, str, str]:
    """
    Parse composite user_id in the format: 'actualUserID_sessionID_projectID'
    Returns: (user_id_actual, session_id, project_id)
    """
    try:
        user_id_parts = raw_user_id.split('_', 2)
        if len(user_id_parts) == 3:
            return user_id_parts[0], user_id_parts[1], user_id_parts[2]
        elif len(user_id_parts) == 2:
            return user_id_parts[0], user_id_parts[1], ''
        else:
            # Single user ID, create session and project IDs
            return raw_user_id, raw_user_id, 'whatsapp'
    except Exception as e:
        logger.warning(f"Error parsing user_id {raw_user_id}: {e}")
        return raw_user_id, raw_user_id, 'whatsapp'

def extract_token_counts(response_metadata) -> Dict[str, int]:
    """
    Extract token counts from LLM response usage metadata.
    Similar to bitenxt_api.py pattern
    """
    if not response_metadata:
        return {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}

    return {
        "input_tokens": getattr(response_metadata, 'prompt_token_count', 0),
        "output_tokens": getattr(response_metadata, 'candidates_token_count', 0),
        "total_tokens": getattr(response_metadata, 'total_token_count', 0)
    }

# ============== Core Database Functions ==============

async def insert_chat_message(
    user_id: str,
    session_id: str,
    project_id: str,
    user_query: str,
    query_token: int,
    llm_response: str,
    llm_token: int,
    response_time_ms: int = None,
    car_context: str = None,
    interaction_type: str = None
) -> Optional[int]:
    """Insert chat message into database (similar to bitenxt_api.py)"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            new_message_id = await conn.fetchval(
                """
                INSERT INTO chat_history (
                    user_id, session_id, project_id, user_query,
                    query_token, llm_response, llm_token, timestamp,
                    channel, response_time_ms, car_context, interaction_type
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, NOW(), 'whatsapp', $8, $9, $10
                )
                RETURNING message_id;
                """,
                user_id, session_id, project_id, user_query,
                query_token, llm_response, llm_token, response_time_ms,
                car_context, interaction_type
            )
            return new_message_id
    except Exception as e:
        logger.error(f"Database error during message insertion: {e}")
        return None

async def insert_user_interaction(
    user_id: str,
    session_id: str,
    interaction_type: str,
    interaction_data: Dict = None,
    car_name: str = None
) -> Optional[int]:
    """Insert user interaction into database"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            interaction_id = await conn.fetchval(
                """
                INSERT INTO user_interactions (
                    user_id, session_id, interaction_type, interaction_data, car_name
                ) VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
                """,
                user_id, session_id, interaction_type,
                json.dumps(interaction_data) if interaction_data else None,
                car_name
            )
            return interaction_id
    except Exception as e:
        logger.error(f"Database error during interaction insertion: {e}")
        return None

async def insert_service_booking(
    user_id: str,
    session_id: str,
    booking_type: str,
    customer_data: Dict
) -> Optional[int]:
    """Insert service booking into database"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            booking_id = await conn.fetchval(
                """
                INSERT INTO service_bookings (
                    user_id, session_id, booking_type, customer_name, phone_number,
                    email, car_model, preferred_date, preferred_time, message
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING id;
                """,
                user_id, session_id, booking_type,
                customer_data.get("name"),
                customer_data.get("phone"),
                customer_data.get("email"),
                customer_data.get("car_model"),
                customer_data.get("preferred_date"),
                customer_data.get("preferred_time"),
                customer_data.get("message")
            )
            return booking_id
    except Exception as e:
        logger.error(f"Database error during booking insertion: {e}")
        return None

async def create_live_chat_session(user_id: str, session_id: str) -> Optional[str]:
    """Create live chat session and return session token"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        import uuid
        session_token = str(uuid.uuid4())
        
        async with db_pool.acquire() as conn:
            chat_session_id = await conn.fetchval(
                """
                INSERT INTO live_chat_sessions (user_id, session_id, session_token)
                VALUES ($1, $2, $3)
                RETURNING id;
                """,
                user_id, session_id, session_token
            )
            
            if chat_session_id:
                logger.info(f"✅ Live chat session {chat_session_id} created for user {user_id}")
                return session_token
            return None
    except Exception as e:
        logger.error(f"Database error during live chat session creation: {e}")
        return None

# ============== Synchronous Wrapper Functions ==============

def run_async_function(coro):
    """Run async function in sync context"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, we can't use run_until_complete
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        # No event loop, create one
        return asyncio.run(coro)

def sync_insert_chat_message(
    user_id: str,
    session_id: str,
    project_id: str,
    user_query: str,
    query_token: int,
    llm_response: str,
    llm_token: int,
    response_time_ms: int = None,
    car_context: str = None,
    interaction_type: str = None
) -> Optional[int]:
    """Synchronous wrapper for insert_chat_message"""
    return run_async_function(insert_chat_message(
        user_id, session_id, project_id, user_query, query_token,
        llm_response, llm_token, response_time_ms, car_context, interaction_type
    ))

def sync_insert_user_interaction(
    user_id: str,
    session_id: str,
    interaction_type: str,
    interaction_data: Dict = None,
    car_name: str = None
) -> Optional[int]:
    """Synchronous wrapper for insert_user_interaction"""
    return run_async_function(insert_user_interaction(
        user_id, session_id, interaction_type, interaction_data, car_name
    ))

def sync_insert_service_booking(
    user_id: str,
    session_id: str,
    booking_type: str,
    customer_data: Dict
) -> Optional[int]:
    """Synchronous wrapper for insert_service_booking"""
    return run_async_function(insert_service_booking(
        user_id, session_id, booking_type, customer_data
    ))

def sync_create_live_chat_session(user_id: str, session_id: str) -> Optional[str]:
    """Synchronous wrapper for create_live_chat_session"""
    return run_async_function(create_live_chat_session(user_id, session_id))

# ============== Database Initialization ==============

def initialize_database():
    """Initialize database connection (called on module import)"""
    try:
        run_async_function(initialize_db_pool())
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.warning(f"Database initialization failed, continuing without DB: {e}")

# Auto-initialize when module is imported
try:
    initialize_database()
except Exception as e:
    logger.warning(f"Database auto-initialization failed: {e}")

# ============== Query Functions ==============

async def get_chat_history(
    user_id: str,
    session_id: str,
    limit: int = 100,
    offset: int = 0
) -> List[Dict]:
    """Get chat history for a user session"""
    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return []

    try:
        async with db_pool.acquire() as conn:
            records = await conn.fetch(
                """
                SELECT
                    message_id, user_id, session_id, project_id,
                    user_query, query_token, llm_response, llm_token,
                    timestamp, response_time_ms, car_context, interaction_type
                FROM chat_history
                WHERE user_id = $1 AND session_id = $2
                ORDER BY timestamp ASC
                LIMIT $3 OFFSET $4;
                """,
                user_id, session_id, limit, offset
            )
            return [dict(record) for record in records]
    except Exception as e:
        logger.error(f"Database error during history retrieval: {e}")
        return []

def sync_get_chat_history(
    user_id: str,
    session_id: str,
    limit: int = 100,
    offset: int = 0
) -> List[Dict]:
    """Synchronous wrapper for get_chat_history"""
    return run_async_function(get_chat_history(user_id, session_id, limit, offset))
