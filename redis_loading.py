# ============================================================================
# CAR DATA LOADING SYSTEM - Redis Integration
# ============================================================================

# Redis connection setup
import json
import redis

from utility import load_text_file


try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0)
    # Test connection
    redis_client.ping()
    print("✅ Connected to Redis successfully")
except Exception as e:
    print(f"❌ Redis connection failed: {e}")
    redis_client = None

def load_car_json(file_path):
    """Load a car JSON file and return its content (fallback method)"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: Car file not found: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file {file_path}: {e}")
        return {}

def load_car_data_from_redis(redis_key):
    """Load car data from Redis using the provided key"""
    if not redis_client:
        print(f"❌ Redis not available, cannot fetch data for key: {redis_key}")
        return {}

    try:
        data = redis_client.get(redis_key)
        if data:
            json_data = json.loads(data.decode('utf-8'))
            print(f"✅ Successfully loaded data from Redis for key: {redis_key}")
            return json_data
        else:
            print(f"❌ Key '{redis_key}' not found in Redis")
            return {}
    except Exception as e:
        print(f"❌ Error fetching data from Redis for key '{redis_key}': {e}")
        return {}

def get_car_data_from_redis_by_name(car_name):
    """
    Dynamically get car data from Redis based on car name
    Maps car names to Redis keys and fetches data on demand
    """
    # Mapping of car names to Redis keys (based on actual Redis keys available)
    car_name_to_redis_key = {
        # Arena Cars (currently available in Redis)
        'Alto K10': 'alto_k10',
        'Brezza': 'brezza',  # Add when available in Redis
        'Celerio': 'celerio',  # Add when available in Redis
        'Dzire': 'dzire',  # Add when available in Redis
        'Eeco': 'eeco',  # Add when available in Redis
        'Ertiga': 'ertiga',  # Add when available in Redis
        'S-Presso': 's_presso',  # Add when available in Redis
        'Swift': 'swift',  # Add when available in Redis
        'Wagon-R': 'wagon_r',  # Add when available in Redis
        'S-presso': 's presso',  # Add when available in Redis

        #NEXA Cars (add when available in Redis)
        'Baleno': 'baleno',  # Add when available in Redis
        'Ciaz': 'ciaz',  # Add when available in Redis
        'Fronx': 'fronx',  # Add when available in Redis
        'Grand Vitara': 'grand_vitara',  # Add when available in Redis
        'Ignis': 'ignis',  # Add when available in Redis
        'Jimny': 'jimny',  # Add when available in Redis
        'XL6': 'xl6',  # Add when available in Redis

        # Tata Commercials (currently available in Redis)
        "Ace-Pro EV": "ace_pro_ev",
        "Ace-Pro Petrol": "ace_pro_petrol",
        "Intra V10": "tata_intra_v10",
        "Yodha-CNG": "yodha_cng",

        "Ace CNG 2.0 Bi-Fuel": "ace_cng_2.0_bi-fuel",
        "Intra V20 Gold": "tata_intra_v20_gold",
        "Ace Gold Petrol": "tata_ace_gold_petrol",
        "Yodha Crew Cab 4x4": "yodha_crew_cab_4x4",
        'Jimny': 'jimny',
        "Yodha Crew Cab 4x4": "yodha_crew_cab_4x4",
        "Yodha EX": "tata_yodha_ex",
        "Intra EV": "tata_intra_ev",
        "Ace Pro Petrol": "ace_pro_petrol",
        "Yodha EX Single Cab": "yodha_ex_single_cab",
        "Ace Diesel": "tata_ace_diesel",
        "Intra V10": "tata_intra_v10",
        "Intra V20 Winger": "intra_v20_winger",
        "Ace EV": "tata_ace_ev",
        "Yodha 1700": "tata_yodha_1700",
        "Ace Pro": "tata_ace_pro",
        "Yodha 1200": "tata_yodha_1200",
        "Ace Gold CNG Plus": "ace_gold_cng_plus",
        "Ace EV 1000": "ace_ev_1000",
        "Yodha 2.0": "tata_yodha_2.0",
        'Intra V20 Winger': 'intra_v20_winger',
        'Ace EV': 'tata_ace_ev',
        'Yodha 1700': 'tata_yodha_1700',
        'Ace Pro': 'tata_ace_pro',
        'Yodha 1200': 'tata_yodha_1200',
        'Ace Gold CNG Plus': 'ace_gold_cng_plus',
        'Ace EV 1000': 'ace_ev_1000',
        'Yodha 2.0': 'tata_yodha_2.0',
        'Main Menu': 'main_menu',
        'Alto K10': 'alto_k10',
        'Ace Pro Bi-Fuel': 'ace_pro_bi-fuel',
        'Ace Flex Fuel': 'tata_ace_flex_fuel',
        'Yodha EX Crew Cab': 'yodha_ex_crew_cab',
        'Ace Pro EV': 'ace_pro_ev',
        'Ace HT+': 'tata_ace_ht+',
        'Intra V70 Gold': 'tata_intra_v70_gold',
        'Ace Zip': 'tata_ace_zip',
        'Intra V30 Gold': 'tata_intra_v30_gold',
        'Intra V50 Gold': 'tata_intra_v50_gold',
        'Yodha CNG': 'yodha_cng',
        'Ace Gold Diesel': 'tata_ace_gold_diesel',
        'Yodha Crew Cab': 'tata_yodha_crew_cab',
        'Tata Prima 2830K HRT': 'tata_prima_2830k_hrt',
        'Tata Prima 2830K SRT': 'tata_prima_2830k_srt',
        'Tata Prima 2830K REPTO': 'prima_2830.k_repto',
        'Tata Prima 3530K HRT': 'prima_3530.k_hrt',
        'Tata Prima 3530K SRT': 'prima_3530.k_srt',
        'Tata Prima 5530.S': 'prima_5530.s',
        'Tata LPK 1416': 'tata_lpk_1416',
        'Tata LPK 2821K FE+ RMC': 'tata_lpk_2821.k_fe+_rmc',
        'Tata LPT 709G': 'tata_lpt_709g',
        'Tata LPT 710': 'tata_lpt_710',
        'Tata LPT 712': 'tata_lpt_712',
        'Tata LPT 912': 'tata_lpt_912',
        'Tata LPT 1009G': 'tata_lpt_1009g',
        'Tata LPT 1012': 'tata_lpt_1012',
        'Tata LPT 1109G': 'tata_lpt_1109g',
        'Tata LPT 1112': 'tata_lpt_1112',
        'Tata LPT 1212': 'tata_lpt_1212',
        'Tata LPT 1216': 'tata_lpt_1216',
        'Tata LPT 1416': 'tata_lpt_1416',
        'Tata LPT 1612G': 'tata_lpt_1612g',
        'Tata LPT 1616': 'tata_lpt_1616',
        'Tata LPT 1816': 'tata_lpt_1816',
        'Tata LPT 1916': 'tata_lpt_1916',
        'Tata LPT GOLD 710': 'tata_lpt_gold_710',
        'Tata SFC 407 GOLD': 'tata_sfc_407_gold',
        'Tata SFC 712': 'tata_sfc_712',
        'Tata Signa 1923.K': 'tata_signa_1923.k',
        'Tata Signa 2821.T': 'tata_signa_2821.t',
        'Tata Signa 2823.T': 'tata_signa_2823.t',
        'Tata Signa 2830.K 8 CUM REPTO RMC': 'tata_signa_2830.k_8_cum_repto_rmc',
        'Tata Signa 3125.T': 'tata_signa_3125.t',
        'Tata Signa 3525.T': 'tata_signa_3525.t',
        'Tata Signa 4021.S': 'tata_signa_4021.s',
        'Tata Signa 4025.S': 'tata_signa_4025.s',
        'Tata Signa 4225.T': 'tata_signa_4225.t',
        'Tata Signa 4623.S': 'tata_signa_4623.s',
        'Tata Signa 4830.T': 'tata_signa_4830.t',
        'Tata Signa 4830.TK FBV': 'tata_signa_4830.tk.fbv',
        'Tata Signa 4930.T': 'tata_signa_4930.t',
        'Tata Signa 5530.S': 'tata_signa_5530.s',
        'Tata ULTRA K.14': 'tata_ultra_k.14',
        'Tata ULTRA T.6': 'tata_ultra_t.6',
        'Tata ULTRA T.7': 'tata_ultra_t.7',
        'Tata ULTRA T.9': 'tata_ultra_t.9',
        'Tata ULTRA T.11': 'tata_ultra_t.11',
        'Tata ULTRA T.16': 'tata_ultra_t.16',
        'Tata ULTRA T.18': 'tata_ultra_t.18',
        'Tata ULTRA T.19': 'tata_ultra_t.19',

        # Main Menu
        'Main Menu': 'main_menu'
    }

    redis_key = car_name_to_redis_key.get(car_name)
    if redis_key:
        return load_car_data_from_redis(redis_key)
    else:
        print(f"❌ No Redis key mapping found for car: {car_name}")
        return {}

# Load Arena Cars from Redis (only available ones)
ALTO_K10_DATA = load_car_data_from_redis('alto_k10')
BREZZA_DATA = load_car_data_from_redis('brezza')  # Add when available in Redis
CELERIO_DATA = load_car_data_from_redis('celerio')  # Add when available in Redis
DZIRE_DATA = load_car_data_from_redis('dzire')  # Add when available in Redis
EECO_DATA = load_car_data_from_redis('eeco')  # Add when available in Redis
ERTIGA_DATA = load_car_data_from_redis('ertiga')  # Add when available in Redis
S_PRESSO_DATA = load_car_data_from_redis('s-presso')  # Add when available in Redis
SWIFT_DATA = load_car_data_from_redis('swift')  # Add when available in Redis
WAGON_R_DATA = load_car_data_from_redis('wagonr')  # Add when available in Redis

# Load NEXA Cars from Redis (add when available)
BALENO_DATA = load_car_data_from_redis('baleno')  # Add when available in Redis
CIAZ_DATA = load_car_data_from_redis('ciaz')  # Add when available in Redis
FRONX_DATA = load_car_data_from_redis('fronx')  # Add when available in Redis
GRAND_VITARA_DATA = load_car_data_from_redis('grand_vitara')  # Add when available in Redis
IGNIS_DATA = load_car_data_from_redis('ignis')  # Add when available in Redis
JIMNY_DATA = load_car_data_from_redis('jimny')  # Add when available in Redis
XL6_DATA = load_car_data_from_redis('xl6')  # Add when available in Redis

# Load Tata Commercials from Redis (available ones)
ACE_GOLD_CNG_PLUS_DATA = load_car_data_from_redis('ace_gold_cng_plus')
TATA_ACE_GOLD_PETROL_DATA = load_car_data_from_redis('tata_ace_gold_petrol')
TATA_ACE_FLEX_FUEL_DATA = load_car_data_from_redis('tata_ace_flex_fuel')
ACE_PRO_BI_FUEL_DATA = load_car_data_from_redis('ace_pro_bi-fuel')
ACE_PRO_PETROL_DATA = load_car_data_from_redis('ace_pro_petrol')
ACE_CNG_2_0_BI_FUEL_DATA = load_car_data_from_redis('ace_cng_2.0_bi-fuel')
TATA_ACE_ZIP_DATA = load_car_data_from_redis('tata_ace_zip')
TATA_INTRA_EV_DATA = load_car_data_from_redis('tata_intra_ev')
TATA_YODHA_CREW_CAB_DATA = load_car_data_from_redis('tata_yodha_crew_cab')
ACE_EV_1000_DATA = load_car_data_from_redis('ace_ev_1000')
TATA_INTRA_V20_GOLD_DATA = load_car_data_from_redis('tata_intra_v20_gold')
TATA_YODHA_1200_DATA = load_car_data_from_redis('tata_yodha_1200')
YODHA_EX_CREW_CAB_DATA = load_car_data_from_redis('yodha_ex_crew_cab')
TATA_ACE_EV_DATA = load_car_data_from_redis('tata_ace_ev')
TATA_YODHA_2_0_DATA = load_car_data_from_redis('tata_yodha_2.0')
TATA_ACE_HT_PLUS_DATA = load_car_data_from_redis('tata_ace_ht+')
YODHA_CREW_CAB_4X2_DATA = load_car_data_from_redis('yodha_crew_cab_4x2')
TATA_YODHA_EX_DATA = load_car_data_from_redis('tata_yodha_ex')
TATA_ACE_GOLD_DIESEL_DATA = load_car_data_from_redis('tata_ace_gold_diesel')
YODHA_CREW_CAB_4X4_DATA = load_car_data_from_redis('yodha_crew_cab_4x4')
ACE_PRO_EV_DATA = load_car_data_from_redis('ace_pro_ev')
TATA_INTRA_V70_GOLD_DATA = load_car_data_from_redis('tata_intra_v70_gold')
TATA_YODHA_1700_DATA = load_car_data_from_redis('tata_yodha_1700')
YODHA_EX_SINGLE_CAB_DATA = load_car_data_from_redis('yodha_ex_single_cab')
INTRA_V20_WINGER_DATA = load_car_data_from_redis('intra_v20_winger')
TATA_INTRA_V30_GOLD_DATA = load_car_data_from_redis('tata_intra_v30_gold')
TATA_INTRA_V50_GOLD_DATA = load_car_data_from_redis('tata_intra_v50_gold')
TATA_ACE_DIESEL_DATA = load_car_data_from_redis('tata_ace_diesel')
ALTO_K10_DATA = load_car_data_from_redis('alto_k10')
YODHA_CNG_DATA = load_car_data_from_redis('yodha_cng')
TATA_INTRA_V10_DATA = load_car_data_from_redis('tata_intra_v10')
TATA_PRIMA_2830K_HRT_DATA = load_car_data_from_redis('tata_prima_2830k_hrt')
TATA_PRIMA_2830K_SRT_DATA = load_car_data_from_redis('tata_prima_2830k_srt')
PRIMA_2830K_REPTO_DATA = load_car_data_from_redis('prima_2830.k_repto')
PRIMA_3530K_HRT_DATA = load_car_data_from_redis('prima_3530.k_hrt')
PRIMA_3530K_SRT_DATA = load_car_data_from_redis('prima_3530.k_srt')
PRIMA_5530S_DATA = load_car_data_from_redis('prima_5530.s')

TATA_LPK_1416_DATA = load_car_data_from_redis('tata_lpk_1416')
TATA_LPK_2821K_FE_PLUS_RMC_DATA = load_car_data_from_redis('tata_lpk_2821.k_fe+_rmc')

TATA_LPT_709G_DATA = load_car_data_from_redis('tata_lpt_709g')
TATA_LPT_710_DATA = load_car_data_from_redis('tata_lpt_710')
TATA_LPT_712_DATA = load_car_data_from_redis('tata_lpt_712')
TATA_LPT_912_DATA = load_car_data_from_redis('tata_lpt_912')
TATA_LPT_1009G_DATA = load_car_data_from_redis('tata_lpt_1009g')
TATA_LPT_1012_DATA = load_car_data_from_redis('tata_lpt_1012')
TATA_LPT_1109G_DATA = load_car_data_from_redis('tata_lpt_1109g')
TATA_LPT_1112_DATA = load_car_data_from_redis('tata_lpt_1112')
TATA_LPT_1212_DATA = load_car_data_from_redis('tata_lpt_1212')
TATA_LPT_1216_DATA = load_car_data_from_redis('tata_lpt_1216')
TATA_LPT_1416_DATA = load_car_data_from_redis('tata_lpt_1416')
TATA_LPT_1612G_DATA = load_car_data_from_redis('tata_lpt_1612g')
TATA_LPT_1616_DATA = load_car_data_from_redis('tata_lpt_1616')
TATA_LPT_1816_DATA = load_car_data_from_redis('tata_lpt_1816')
TATA_LPT_1916_DATA = load_car_data_from_redis('tata_lpt_1916')
TATA_LPT_GOLD_710_DATA = load_car_data_from_redis('tata_lpt_gold_710')

TATA_SFC_407_GOLD_DATA = load_car_data_from_redis('tata_sfc_407_gold')
TATA_SFC_712_DATA = load_car_data_from_redis('tata_sfc_712')

TATA_SIGNA_1923K_DATA = load_car_data_from_redis('tata_signa_1923.k')
TATA_SIGNA_2821T_DATA = load_car_data_from_redis('tata_signa_2821.t')
TATA_SIGNA_2823T_DATA = load_car_data_from_redis('tata_signa_2823.t')
TATA_SIGNA_2830K_8_CUM_REPTO_RMC_DATA = load_car_data_from_redis('tata_signa_2830.k_8_cum_repto_rmc')
TATA_SIGNA_3125T_DATA = load_car_data_from_redis('tata_signa_3125.t')
TATA_SIGNA_3525T_DATA = load_car_data_from_redis('tata_signa_3525.t')
TATA_SIGNA_4021S_DATA = load_car_data_from_redis('tata_signa_4021.s')
TATA_SIGNA_4025S_DATA = load_car_data_from_redis('tata_signa_4025.s')
TATA_SIGNA_4225T_DATA = load_car_data_from_redis('tata_signa_4225.t')
TATA_SIGNA_4623S_DATA = load_car_data_from_redis('tata_signa_4623.s')
TATA_SIGNA_4830T_DATA = load_car_data_from_redis('tata_signa_4830.t')
TATA_SIGNA_4830TK_FBV_DATA = load_car_data_from_redis('tata_signa_4830.tk.fbv')
TATA_SIGNA_4930T_DATA = load_car_data_from_redis('tata_signa_4930.t')
TATA_SIGNA_5530S_DATA = load_car_data_from_redis('tata_signa_5530.s')

TATA_ULTRA_K14_DATA = load_car_data_from_redis('tata_ultra_k.14')
TATA_ULTRA_T6_DATA = load_car_data_from_redis('tata_ultra_t.6')
TATA_ULTRA_T7_DATA = load_car_data_from_redis('tata_ultra_t.7')
TATA_ULTRA_T9_DATA = load_car_data_from_redis('tata_ultra_t.9')
TATA_ULTRA_T11_DATA = load_car_data_from_redis('tata_ultra_t.11')
TATA_ULTRA_T16_DATA = load_car_data_from_redis('tata_ultra_t.16')
TATA_ULTRA_T18_DATA = load_car_data_from_redis('tata_ultra_t.18')
TATA_ULTRA_T19_DATA = load_car_data_from_redis('tata_ultra_t.19')

# Load Tata Magic and Winger models from Redis (available ones)
TATA_MAGIC_DATA = load_car_data_from_redis('tata_magic')
TATA_MAGIC_AMBULANCE_DATA = load_car_data_from_redis('tata_magic_ambulance')
TATA_WINGER_12S_DATA = load_car_data_from_redis('tata_winger_12s')
TATA_WINGER_AMB_TYPE_D_BS6_7P_AC_SI_DATA = load_car_data_from_redis('tata_winger_amb_type_d_bs6_7+p_ac_si')
TATA_WINGER_AMBULANCE_3200_DATA = load_car_data_from_redis('tata_winger_ambulance_3200')
TATA_WINGER_FL_3200_FR_12S_DATA = load_car_data_from_redis('tata_winger_fl_3200_fr_12s')
TATA_WINGER_FL_3200_FR_13S_AC_DATA = load_car_data_from_redis('tata_winger_fl_3200_fr_13s_ac')
TATA_WINGER_FL_3200_FR_9S_AC_DATA = load_car_data_from_redis('tata_winger_fl_3200_fr_9s_ac')
TATA_WINGER_FL_3488_HR_15S_AC_DATA = load_car_data_from_redis('tata_winger_fl_3488_hr_15s_ac')
TATA_WINGER_FL_AMB_B_35_8P_AC_DATA = load_car_data_from_redis('tata_winger_fl_amb-b_35_8+p_ac')
TATA_MAGIC_EXPRESS_10_SEATER_DATA = load_car_data_from_redis('tata_magic_express_10_seater')
TATA_MAGIC_EXPRESS_CNG_DATA = load_car_data_from_redis('tata_magic_express_cng')
TATA_MAGIC_EXPRESS_YELLOW_10_SEATER_DATA = load_car_data_from_redis('tata_magic_express_yellow_10_seater')
TATA_MAGIC_MANTRA_10_SEATER_DATA = load_car_data_from_redis('tata_magic_mantra_10_seater')
TATA_WINGER_FL_SK_3200_FR_18S_DATA = load_car_data_from_redis('tata_winger_fl_sk_3200_fr_18s')
TATA_WINGER_FL_SK_3488_HR_21S_DATA = load_car_data_from_redis('tata_winger_fl_sk_3488_hr_21s')
WINGER_AMB_TYPE_C_BS6_7P_AC_SI_DATA = load_car_data_from_redis('winger_amb_type_c_bs6_7+p_ac_si')
WINGER_FL_3200_FR_12S_AC_DATA = load_car_data_from_redis('winger_fl_3200_fr_12s_ac')
WINGER_FL_3200_FR_13S_NAC_DATA = load_car_data_from_redis('winger_fl_3200_fr_13s_nac')
WINGER_FL_3488_HR_12S_AC_DATA = load_car_data_from_redis('winger_fl_3488_hr_12s_ac')
WINGER_FL_3488_HR_12S_DATA = load_car_data_from_redis('winger_fl_3488_hr_12s')
WINGER_FL_35_HR_PAS_15S_DATA = load_car_data_from_redis('winger_fl_35_hr_pas_15s')
WINGER_FL_AMB_32SH_10P_AC_DATA = load_car_data_from_redis('winger_fl_amb_32sh_10+p_ac')
WINGER_FL_AMB_32SH_10P_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_fl_amb_32sh_10+p_bs6_phase_2')
WINGER_FL_AMB_35SH_10P_AC_DATA = load_car_data_from_redis('winger_fl_amb_35sh_10+p_ac')
WINGER_FL_AMB_35SH_10P_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_fl_amb_35sh_10+p_bs6_phase_2')
WINGER_FL_AMB_B_3200_7P_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_fl_amb-b_3200_7+p_bs6_phase_2')
WINGER_FL_AMB_B_35_8P_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_fl_amb-b_35_8+p_bs6_phase_2')
WINGER_FL_AMBB_3200_7P_AC_DATA = load_car_data_from_redis('winger_fl_ambb_3200_7+p_ac')
WINGER_RDE_9S_32WB_PAS_FR_AC_DATA = load_car_data_from_redis('winger_rde_9s_32wb_pas_fr_ac')
WINGER_21S_35WB_SCH_HR_FPS_DATA = load_car_data_from_redis('winger_21s_35wb_sch_hr_fps')
WINGER_FL_35_HR_PAS_15S_SHL_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_fl_35_hr_pas_15s_shl_bs6_phase_2')
WINGER_FL_SK_3200_FR_13S_AC_DATA = load_car_data_from_redis('winger_fl_sk_3200_fr_13s_ac')
WINGER_RDE_21S_35WB_SCH_HR_BS6_PHASE_2_DATA = load_car_data_from_redis('winger_rde_21s_35wb_sch_hr_bs6_phase_2')
# --------- CITYRIDE SKL ---------
CITYRIDE_SKL_19_B_D_LP_410_30_DATA = load_car_data_from_redis('cityride_skl_19+b+d_lp_410_30')
CITYRIDE_SKL_19_C_D_LP_410_31_DATA = load_car_data_from_redis('cityride_skl_19+c+d_lp_410_31')
CITYRIDE_SKL_27_A_D_LP_410_33_DATA = load_car_data_from_redis('cityride_skl_27+a+d_lp_410_33')
CITYRIDE_SKL_27_A_D_LP410_33_DATA = load_car_data_from_redis('cityride_skl_27+a+d_lp410_33')
CITYRIDE_SKL_29_A_D_LP412_36_DATA = load_car_data_from_redis('cityride_skl_29+a+d_lp412_36')
CITYRIDE_SKL_35_A_D_LP412_36_DATA = load_car_data_from_redis('cityride_skl_35+a+d_lp412_36')
CITYRIDE_SKL_37_A_D_LP712_45_DATA = load_car_data_from_redis('cityride_skl_37+a+d_lp712_45')
CITYRIDE_SKL_41_A_D_LP412_36_DATA = load_car_data_from_redis('cityride_skl_41+a+d_lp412_36')
CITYRIDE_SKL_41_A_D_LP812_52_DATA = load_car_data_from_redis('cityride_skl_41+a+d_lp812_52')
CITYRIDE_SKL_45_A_D_LP712_45_DATA = load_car_data_from_redis('cityride_skl_45+a+d_lp712_45')
CITYRIDE_SKL_50_A_D_LP812_52_DATA = load_car_data_from_redis('cityride_skl_50+a+d_lp812_52')
CITYRIDE_SKL_53_A_D_LP712_45_DATA = load_car_data_from_redis('cityride_skl_53+a+d_lp712_45')
CITYRIDE_SKL_59_A_D_LP812_52_DATA = load_car_data_from_redis('cityride_skl_59+a+d_lp812_52')

# --------- LP / LPO ---------
LP_410_31_DIESEL_DATA = load_car_data_from_redis('lp_410_31_diesel')
LP_410_33_DIESEL_DATA = load_car_data_from_redis('lp_410_33_diesel')
LP_410_36_CNG_DATA = load_car_data_from_redis('lp_410_36_cng')
LP_412_36_DIESEL_DATA = load_car_data_from_redis('lp_412_36_diesel')
LP_712_39_DIESEL_DATA = load_car_data_from_redis('lp_712_39_diesel')
LP_712_45_DIESEL_DATA = load_car_data_from_redis('lp_712_45_diesel')
LP_716_45_DIESEL_DATA = load_car_data_from_redis('lp_716_45_diesel')
LP_812_52_DIESEL_DATA = load_car_data_from_redis('lp_812_52_diesel')
LP_910_52_CNG_DATA = load_car_data_from_redis('lp_910_52_cng')
LP_913_52_CNG_DATA = load_car_data_from_redis('lp_913_52_cng')
LP_916_52_DIESEL_DATA = load_car_data_from_redis('lp_916_52_diesel')
LPO_11_6_52_CNG_DATA = load_car_data_from_redis('lpo_11.6_52_cng')
LPO_11_6_54_DIESEL_DATA = load_car_data_from_redis('lpo_11.6_54_diesel')
LPO_1316_54_DIESEL_DATA = load_car_data_from_redis('lpo_1316_54_diesel')
LPO_1316_57_DIESEL_DATA = load_car_data_from_redis('lpo_1316_57_diesel')
LPO_8_6_44_DIESEL_DATA = load_car_data_from_redis('lpo_8.6_44_diesel')

# --------- STARBUS ---------
STARBUS_19_B_D_LP_410_30_DATA = load_car_data_from_redis('starbus_19+b+d_lp_410_30')
STARBUS_PRIME_34_A_D_AC410_36G_DATA = load_car_data_from_redis('starbus_prime_34+a+d_ac410_36g')
STARBUS_PRIME_34_A_D_LP410_36G_DATA = load_car_data_from_redis('starbus_prime_34+a+d_lp410_36g')
STARBUS_PRIME_34_A_D_LP412_36_DATA = load_car_data_from_redis('starbus_prime_34+a+d_lp412_36')
STARBUS_PRIME_38_A_D_LP410_36G_DATA = load_car_data_from_redis('starbus_prime_38+a+d_lp410_36g')
STARBUS_PRIME_38_A_D_LP412_36_DATA = load_car_data_from_redis('starbus_prime_38+a+d_lp412_36')
STARBUS_PRIME_46_A_D_AC_712_45_DATA = load_car_data_from_redis('starbus_prime_46+a+d_ac_712_45')
STARBUS_PRIME_46_A_D_LP712_45_DATA = load_car_data_from_redis('starbus_prime_46+a+d_lp712_45')
STARBUS_PRIME_50_D_AC_LP916_52_DATA = load_car_data_from_redis('starbus_prime_50+d_ac_lp916_52')
STARBUS_PRIME_51_A_D_916_52_DATA = load_car_data_from_redis('starbus_prime_51+a+d_916_52')
STARBUS_PRIME_51_A_D_AC_913_52G_DATA = load_car_data_from_redis('starbus_prime_51+a+d_ac_913_52g')
STARBUS_PRIME_51_A_D_AC_916_52_DATA = load_car_data_from_redis('starbus_prime_51+a+d_ac_916_52')
STARBUS_PRIME_51_A_D_LP812_52_DATA = load_car_data_from_redis('starbus_prime_51+a+d_lp812_52')
STARBUS_PRIME_51_A_D_LP916_52_DATA = load_car_data_from_redis('starbus_prime_51+a+d_lp916_52')
STARBUS_PRIME_53_A_D_LP712_45_DATA = load_car_data_from_redis('starbus_prime_53+a+d_lp712_45')
STARBUS_PRIME_59_A_D_LP812_52_DATA = load_car_data_from_redis('starbus_prime_59+a+d_lp812_52')
STARBUS_SKL_19_A_D_LP_410_29_DATA = load_car_data_from_redis('starbus_skl_19+a+d_lp_410_29')

# --------- ULTRA PRIME ---------
ULTRA_PRIME_58_A_D_LPO11_6_54_DATA = load_car_data_from_redis('ultra_prime_58+a+d_lpo11.6_54')



# Load main menu from Redis (fallback to JSON if not in Redis)
MAIN_MENU_DATA = load_car_data_from_redis('main_menu')
if not MAIN_MENU_DATA:
    print("⚠️ Main menu not found in Redis, falling back to local JSON file")
    MAIN_MENU_DATA = load_car_json('main_menu.json')
    if MAIN_MENU_DATA:
        print("✅ Main menu loaded from local JSON file")
    else:
        print("❌ Main menu not found in Redis or local JSON file")

# Create a unified car data dictionary for easy access (only loaded cars)
CAR_DATA_REGISTRY = {
    # Arena Cars (currently available from Redis)
    'Alto K10': ALTO_K10_DATA,
    'Brezza': BREZZA_DATA,  # Add when available in Redis
    'Celerio': CELERIO_DATA,  # Add when available in Redis
    'Dzire': DZIRE_DATA,  # Add when available in Redis
    'Eeco': EECO_DATA,  # Add when available in Redis
    'Ertiga': ERTIGA_DATA,  # Add when available in Redis
    'S-Presso': S_PRESSO_DATA,  # Add when available in Redis
    'Swift': SWIFT_DATA,  # Add when available in Redis
    'Wagon-R': WAGON_R_DATA,  # Add when available in Redis

    # NEXA Cars (add when available in Redis)
    'Baleno': BALENO_DATA,  # Add when available in Redis
    'Ciaz': CIAZ_DATA,  # Add when available in Redis
    'Fronx': FRONX_DATA,  # Add when available in Redis
    'Grand Vitara': GRAND_VITARA_DATA,  # Add when available in Redis
    'Ignis': IGNIS_DATA,  # Add when available in Redis
    'Jimny': JIMNY_DATA,  # Add when available in Redis
    'XL6': XL6_DATA,  # Add when available in Redis

    # Tata Commercials (currently available from Redis)
    'Ace-Pro EV': ACE_PRO_EV_DATA,
    'Ace-Pro Petrol': ACE_PRO_PETROL_DATA,
    'Intra V10': TATA_INTRA_V10_DATA,
    'Yodha-CNG': YODHA_CNG_DATA,
    'Ace Gold CNG+': ACE_GOLD_CNG_PLUS_DATA,
    'Ace Gold Petrol': TATA_ACE_GOLD_PETROL_DATA,
    'Ace Flex Fuel': TATA_ACE_FLEX_FUEL_DATA,
    'Ace Pro Bi-Fuel': ACE_PRO_BI_FUEL_DATA,
    'Ace CNG 2.0': ACE_CNG_2_0_BI_FUEL_DATA,
    'Ace Zip': TATA_ACE_ZIP_DATA,
    'Intra EV': TATA_INTRA_EV_DATA,
    'Yodha Crew Cab': TATA_YODHA_CREW_CAB_DATA,
    'Ace EV 1000': ACE_EV_1000_DATA,
    'Intra V20 Gold': TATA_INTRA_V20_GOLD_DATA,
    'Yodha 1200': TATA_YODHA_1200_DATA,
    'Yodha EX Crew Cab': YODHA_EX_CREW_CAB_DATA,
    'Ace EV': TATA_ACE_EV_DATA,
    'Yodha 2.0': TATA_YODHA_2_0_DATA,
    'Ace HT+': TATA_ACE_HT_PLUS_DATA,
    'Yodha Crew Cab 4x2': YODHA_CREW_CAB_4X2_DATA,
    'Yodha EX': TATA_YODHA_EX_DATA,
    'Ace Gold Diesel': TATA_ACE_GOLD_DIESEL_DATA,
    'Yodha Crew Cab 4x4': YODHA_CREW_CAB_4X4_DATA,
    'Intra V70 Gold': TATA_INTRA_V70_GOLD_DATA,
    'Yodha 1700': TATA_YODHA_1700_DATA,
    'Yodha EX Single Cab': YODHA_EX_SINGLE_CAB_DATA,
    'Intra V20 Winger': INTRA_V20_WINGER_DATA,
    'Intra V30 Gold': TATA_INTRA_V30_GOLD_DATA,
    'Intra V50 Gold': TATA_INTRA_V50_GOLD_DATA,
    'Ace Diesel': TATA_ACE_DIESEL_DATA,

    # Newly added heavy/commercial models in registry
    'Tata Prima 2830K HRT': TATA_PRIMA_2830K_HRT_DATA,
    'Tata Prima 2830K SRT': TATA_PRIMA_2830K_SRT_DATA,
    'Tata Prima 2830K REPTO': PRIMA_2830K_REPTO_DATA,
    'Tata Prima 3530K HRT': PRIMA_3530K_HRT_DATA,
    'Tata Prima 3530K SRT': PRIMA_3530K_SRT_DATA,
    'Tata Prima 5530.S': PRIMA_5530S_DATA,

    'Tata LPK 1416': TATA_LPK_1416_DATA,
    'Tata LPK 2821.K FE+ RMC': TATA_LPK_2821K_FE_PLUS_RMC_DATA,

    'Tata LPT 709G': TATA_LPT_709G_DATA,
    'Tata LPT 710': TATA_LPT_710_DATA,
    'Tata LPT 712': TATA_LPT_712_DATA,
    'Tata LPT 912': TATA_LPT_912_DATA,
    'Tata LPT 1009G': TATA_LPT_1009G_DATA,
    'Tata LPT 1012': TATA_LPT_1012_DATA,
    'Tata LPT 1109G': TATA_LPT_1109G_DATA,
    'Tata LPT 1112': TATA_LPT_1112_DATA,
    'Tata LPT 1212': TATA_LPT_1212_DATA,
    'Tata LPT 1216': TATA_LPT_1216_DATA,
    'Tata LPT 1416': TATA_LPT_1416_DATA,
    'Tata LPT 1612G': TATA_LPT_1612G_DATA,
    'Tata LPT 1616': TATA_LPT_1616_DATA,
    'Tata LPT 1816': TATA_LPT_1816_DATA,
    'Tata LPT 1916': TATA_LPT_1916_DATA,
    'Tata LPT GOLD 710': TATA_LPT_GOLD_710_DATA,

    'Tata SFC 407 GOLD': TATA_SFC_407_GOLD_DATA,
    'Tata SFC 712': TATA_SFC_712_DATA,

    'Tata Signa 1923.K': TATA_SIGNA_1923K_DATA,
    'Tata Signa 2821.T': TATA_SIGNA_2821T_DATA,
    'Tata Signa 2823.T': TATA_SIGNA_2823T_DATA,
    'Tata Signa 2830.K 8 CUM REPTO RMC': TATA_SIGNA_2830K_8_CUM_REPTO_RMC_DATA,
    'Tata Signa 3125.T': TATA_SIGNA_3125T_DATA,
    'Tata Signa 3525.T': TATA_SIGNA_3525T_DATA,
    'Tata Signa 4021.S': TATA_SIGNA_4021S_DATA,
    'Tata Signa 4025.S': TATA_SIGNA_4025S_DATA,
    'Tata Signa 4225.T': TATA_SIGNA_4225T_DATA,
    'Tata Signa 4623.S': TATA_SIGNA_4623S_DATA,
    'Tata Signa 4830.T': TATA_SIGNA_4830T_DATA,
    'Tata Signa 4830.TK FBV': TATA_SIGNA_4830TK_FBV_DATA,
    'Tata Signa 4930.T': TATA_SIGNA_4930T_DATA,
    'Tata Signa 5530.S': TATA_SIGNA_5530S_DATA,

    'Tata ULTRA K.14': TATA_ULTRA_K14_DATA,
    'Tata ULTRA T.6': TATA_ULTRA_T6_DATA,
    'Tata ULTRA T.7': TATA_ULTRA_T7_DATA,
    'Tata ULTRA T.9': TATA_ULTRA_T9_DATA,
    'Tata ULTRA T.11': TATA_ULTRA_T11_DATA,
    'Tata ULTRA T.16': TATA_ULTRA_T16_DATA,
    'Tata ULTRA T.18': TATA_ULTRA_T18_DATA,
    'Tata ULTRA T.19': TATA_ULTRA_T19_DATA,
    # Tata Magic and Winger models (currently available from Redis)
    'Tata Magic Ambulance': TATA_MAGIC_AMBULANCE_DATA,
    'Tata Winger 12S': TATA_WINGER_12S_DATA,
    'Tata Winger Amb Type D BS6 7+P AC SI': TATA_WINGER_AMB_TYPE_D_BS6_7P_AC_SI_DATA,
    'Tata Winger Ambulance 3200': TATA_WINGER_AMBULANCE_3200_DATA,
    'Tata Winger FL 3200 FR 12S': TATA_WINGER_FL_3200_FR_12S_DATA,
    'Tata Winger FL 3200 FR 13S AC': TATA_WINGER_FL_3200_FR_13S_AC_DATA,
    'Tata Winger FL 3200 FR 9S AC': TATA_WINGER_FL_3200_FR_9S_AC_DATA,
    'Tata Winger FL 3488 HR 15S AC': TATA_WINGER_FL_3488_HR_15S_AC_DATA,
    'Tata Winger FL AMB-B 35 8+P AC': TATA_WINGER_FL_AMB_B_35_8P_AC_DATA,
    'Tata Magic Express 10 Seater': TATA_MAGIC_EXPRESS_10_SEATER_DATA,
    'Tata Magic Express CNG': TATA_MAGIC_EXPRESS_CNG_DATA,
    'Tata Magic Express Yellow 10 Seater': TATA_MAGIC_EXPRESS_YELLOW_10_SEATER_DATA,
    'Tata Magic Mantra 10 Seater': TATA_MAGIC_MANTRA_10_SEATER_DATA,
    'Tata Winger FL SK 3200 FR 18S': TATA_WINGER_FL_SK_3200_FR_18S_DATA,
    'Tata Winger FL SK 3488 HR 21S': TATA_WINGER_FL_SK_3488_HR_21S_DATA,
    'Winger Amb Type C BS6 7+P AC SI': WINGER_AMB_TYPE_C_BS6_7P_AC_SI_DATA,
    'Winger FL 3200 FR 12S AC': WINGER_FL_3200_FR_12S_AC_DATA,
    'Winger FL 3200 FR 13S NAC': WINGER_FL_3200_FR_13S_NAC_DATA,
    'Winger FL 3488 HR 12S AC': WINGER_FL_3488_HR_12S_AC_DATA,
    'Winger FL 3488 HR 12S': WINGER_FL_3488_HR_12S_DATA,
    'Winger FL 35 HR PAS 15S': WINGER_FL_35_HR_PAS_15S_DATA,
    'Winger FL AMB 32SH 10+P AC': WINGER_FL_AMB_32SH_10P_AC_DATA,
    'Winger FL AMB 32SH 10+P BS6 Phase 2': WINGER_FL_AMB_32SH_10P_BS6_PHASE_2_DATA,
    'Winger FL AMB 35SH 10+P AC': WINGER_FL_AMB_35SH_10P_AC_DATA,
    'Winger FL AMB 35SH 10+P BS6 Phase 2': WINGER_FL_AMB_35SH_10P_BS6_PHASE_2_DATA,
    'Winger FL AMB-B 3200 7+P BS6 Phase 2': WINGER_FL_AMB_B_3200_7P_BS6_PHASE_2_DATA,
    'Winger FL AMB-B 35 8+P BS6 Phase 2': WINGER_FL_AMB_B_35_8P_BS6_PHASE_2_DATA,
    'Winger FL AMBB 3200 7+P AC': WINGER_FL_AMBB_3200_7P_AC_DATA,
    'Winger RDE 9S 32WB PAS FR AC': WINGER_RDE_9S_32WB_PAS_FR_AC_DATA,
    'Winger 21S 35WB Sch HR FPS': WINGER_21S_35WB_SCH_HR_FPS_DATA,
    'Winger FL 35 HR PAS 15S SHL BS6 Phase 2': WINGER_FL_35_HR_PAS_15S_SHL_BS6_PHASE_2_DATA,
    'Winger FL SK 3200 FR 13S AC': WINGER_FL_SK_3200_FR_13S_AC_DATA,
    'Winger RDE 21S 35WB Sch HR BS6 Phase 2': WINGER_RDE_21S_35WB_SCH_HR_BS6_PHASE_2_DATA,
    'CITYRIDE SKL 19+B+D LP 410 30': CITYRIDE_SKL_19_B_D_LP_410_30_DATA,
    'CITYRIDE SKL 19+C+D LP 410 31': CITYRIDE_SKL_19_C_D_LP_410_31_DATA,
    'CITYRIDE SKL 27+A+D LP 410 33': CITYRIDE_SKL_27_A_D_LP_410_33_DATA,
    'CITYRIDE SKL 27+A+D LP410 33': CITYRIDE_SKL_27_A_D_LP410_33_DATA,
    'CITYRIDE SKL 29+A+D LP412 36': CITYRIDE_SKL_29_A_D_LP412_36_DATA,
    'CITYRIDE SKL 35+A+D LP412 36': CITYRIDE_SKL_35_A_D_LP412_36_DATA,
    'CITYRIDE SKL 37+A+D LP712 45': CITYRIDE_SKL_37_A_D_LP712_45_DATA,
    'CITYRIDE SKL 41+A+D LP412 36': CITYRIDE_SKL_41_A_D_LP412_36_DATA,
    'CITYRIDE SKL 41+A+D LP812 52': CITYRIDE_SKL_41_A_D_LP812_52_DATA,
    'CITYRIDE SKL 45+A+D LP712 45': CITYRIDE_SKL_45_A_D_LP712_45_DATA,
    'CITYRIDE SKL 50+A+D LP812 52': CITYRIDE_SKL_50_A_D_LP812_52_DATA,
    'CITYRIDE SKL 53+A+D LP712 45': CITYRIDE_SKL_53_A_D_LP712_45_DATA,
    'CITYRIDE SKL 59+A+D LP812 52': CITYRIDE_SKL_59_A_D_LP812_52_DATA,
    'LP 410 31 DIESEL': LP_410_31_DIESEL_DATA,
    'LP 410 33 DIESEL': LP_410_33_DIESEL_DATA,
    'LP 410 36 CNG': LP_410_36_CNG_DATA,
    'LP 412 36 DIESEL': LP_412_36_DIESEL_DATA,
    'LP 712 39 DIESEL': LP_712_39_DIESEL_DATA,
    'LP 712 45 DIESEL': LP_712_45_DIESEL_DATA,
    'LP 716 45 DIESEL': LP_716_45_DIESEL_DATA,
    'LP 812 52 DIESEL': LP_812_52_DIESEL_DATA,
    'LP 910 52 CNG': LP_910_52_CNG_DATA,
    'LP 913 52 CNG': LP_913_52_CNG_DATA,
    'LP 916 52 DIESEL': LP_916_52_DIESEL_DATA,
    'LPO 11.6 52 CNG': LPO_11_6_52_CNG_DATA,
    'LPO 11.6 54 DIESEL': LPO_11_6_54_DIESEL_DATA,
    'LPO 1316 54 DIESEL': LPO_1316_54_DIESEL_DATA,
    'LPO 1316 57 DIESEL': LPO_1316_57_DIESEL_DATA,
    'LPO 8.6 44 DIESEL': LPO_8_6_44_DIESEL_DATA,
    'STARBUS 19+B+D LP 410 30': STARBUS_19_B_D_LP_410_30_DATA,
    'STARBUS PRIME 34+A+D AC410 36G': STARBUS_PRIME_34_A_D_AC410_36G_DATA,
    'STARBUS PRIME 34+A+D LP410 36G': STARBUS_PRIME_34_A_D_LP410_36G_DATA,
    'STARBUS PRIME 34+A+D LP412 36': STARBUS_PRIME_34_A_D_LP412_36_DATA,
    'STARBUS PRIME 38+A+D LP410 36G': STARBUS_PRIME_38_A_D_LP410_36G_DATA,
    'STARBUS PRIME 38+A+D LP412 36': STARBUS_PRIME_38_A_D_LP412_36_DATA,
    'STARBUS PRIME 46+A+D AC 712 45': STARBUS_PRIME_46_A_D_AC_712_45_DATA,
    'STARBUS PRIME 46+A+D LP712 45': STARBUS_PRIME_46_A_D_LP712_45_DATA,
    'STARBUS PRIME 50+D AC LP916 52': STARBUS_PRIME_50_D_AC_LP916_52_DATA,
    'STARBUS PRIME 51+A+D 916 52': STARBUS_PRIME_51_A_D_916_52_DATA,
    'STARBUS PRIME 51+A+D AC 913 52G': STARBUS_PRIME_51_A_D_AC_913_52G_DATA,
    'STARBUS PRIME 51+A+D AC 916 52': STARBUS_PRIME_51_A_D_AC_916_52_DATA,
    'STARBUS PRIME 51+A+D LP812 52': STARBUS_PRIME_51_A_D_LP812_52_DATA,
    'STARBUS PRIME 51+A+D LP916 52': STARBUS_PRIME_51_A_D_LP916_52_DATA,
    'STARBUS PRIME 53+A+D LP712 45': STARBUS_PRIME_53_A_D_LP712_45_DATA,
    'STARBUS PRIME 59+A+D LP812 52': STARBUS_PRIME_59_A_D_LP812_52_DATA,
    'STARBUS SKL 19+A+D LP 410 29': STARBUS_SKL_19_A_D_LP_410_29_DATA,
    'ULTRA PRIME 58+A+D LPO11.6 54': ULTRA_PRIME_58_A_D_LPO11_6_54_DATA,

    # Main Menu
    'Main Menu': MAIN_MENU_DATA,
}

# Alternative name mappings to handle different naming conventions
CAR_NAME_MAPPINGS = {
    'alto k10': 'Alto K10',
    'alto': 'Alto K10',
    'k10': 'Alto K10',
    'brezza': 'Brezza',
    'vitara brezza': 'Brezza',
    'celerio': 'Celerio',
    'dzire': 'Dzire',
    'desire': 'Dzire',
    'eeco': 'Eeco',
    'ertiga': 'Ertiga',
    's-presso': 'S-Presso',
    's presso': 'S-Presso',
    'spresso': 'S-Presso',
    'swift': 'Swift',
    'wagon-r': 'Wagon-R',
    'wagon r': 'Wagon-R',
    'wagonr': 'Wagon-R',
    'baleno': 'Baleno',
    'ciaz': 'Ciaz',
    'fronx': 'Fronx',
    'grand vitara': 'Grand Vitara',
    'vitara': 'Grand Vitara',
    'ignis': 'Ignis',
    'jimny': 'Jimny',
    'xl6': 'XL6',
    'xl-6': 'XL6',

    # Tata Commercials
    'Ace-Pro EV': 'ace_pro_ev',
    'Ace-Pro Petrol': 'ace_pro_petrol',
    'Intra V10': 'tata_intra_v10',
    'Yodha-CNG': 'yodha_cng',
    'Ace CNG 2.0 Bi-Fuel': 'ace_cng_2.0_bi-fuel',
    'Intra V20 Gold': 'tata_intra_v20_gold',
    'Ace Gold Petrol': 'tata_ace_gold_petrol',
    'Jimny': 'jimny',
    'Yodha Crew Cab 4x4': 'yodha_crew_cab_4x4',
    'Yodha EX': 'tata_yodha_ex',
    'Intra EV': 'tata_intra_ev',
    'Ace Pro Petrol': 'ace_pro_petrol',
    'Yodha EX Single Cab': 'yodha_ex_single_cab',
    'Ace Diesel': 'tata_ace_diesel',
    'Intra V10': 'tata_intra_v10',
    'Intra V20 Winger': 'intra_v20_winger',
    'Ace EV': 'tata_ace_ev',
    'Yodha 1700': 'tata_yodha_1700',
    'Ace Pro': 'tata_ace_pro',
    'Yodha 1200': 'tata_yodha_1200',
    'Ace Gold CNG Plus': 'ace_gold_cng_plus',
    'Ace EV 1000': 'ace_ev_1000',
    'Yodha 2.0': 'tata_yodha_2.0',
    'Main Menu': 'main_menu',
    'Alto K10': 'alto_k10',
    'Ace Pro Bi-Fuel': 'ace_pro_bi-fuel',
    'Ace Flex Fuel': 'tata_ace_flex_fuel',
    'Yodha EX Crew Cab': 'yodha_ex_crew_cab',
    'Ace Pro EV': 'ace_pro_ev',
    'Ace HT+': 'tata_ace_ht+',
    'Intra V70 Gold': 'tata_intra_v70_gold',
    'Ace Zip': 'tata_ace_zip',
    'Intra V30 Gold': 'tata_intra_v30_gold',
    'Intra V50 Gold': 'tata_intra_v50_gold',
    'Yodha CNG': 'yodha_cng',
    'Ace Gold Diesel': 'tata_ace_gold_diesel',
    'Yodha Crew Cab': 'tata_yodha_crew_cab',
    'Yodha Crew Cab 4x2': 'yodha_crew_cab_4x2',
    'Tata Prima 2830K HRT': 'tata_prima_2830k_hrt',
    'Tata Prima 2830K SRT': 'tata_prima_2830k_srt',
    'Tata Magic Ambulance': 'tata_magic_ambulance',
'Tata Winger 12S': 'tata_winger_12s',
'Tata Winger Amb Type D BS6 7+P AC SI': 'tata_winger_amb_type_d_bs6_7+p_ac_si',
'Tata Winger Ambulance 3200': 'tata_winger_ambulance_3200',
'Tata Winger FL 3200 FR 12S': 'tata_winger_fl_3200_fr_12s',
'Tata Winger FL 3200 FR 13S AC': 'tata_winger_fl_3200_fr_13s_ac',
'Tata Winger FL 3200 FR 9S AC': 'tata_winger_fl_3200_fr_9s_ac',
'Tata Winger FL 3488 HR 15S AC': 'tata_winger_fl_3488_hr_15s_ac',
'Tata Winger FL AMB-B 35 8+P AC': 'tata_winger_fl_amb-b_35_8+p_ac',
'Tata Magic Express 10 Seater': 'tata_magic_express_10_seater',
'Tata Magic Express CNG': 'tata_magic_express_cng',
'Tata Magic Express Yellow 10 Seater': 'tata_magic_express_yellow_10_seater',
'Tata Magic Mantra 10 Seater': 'tata_magic_mantra_10_seater',
'Tata Winger FL SK 3200 FR 18S': 'tata_winger_fl_sk_3200_fr_18s',
'Tata Winger FL SK 3488 HR 21S': 'tata_winger_fl_sk_3488_hr_21s',
'Winger Amb Type C BS6 7+P AC SI': 'winger_amb_type_c_bs6_7+p_ac_si',
'Winger FL 3200 FR 12S AC': 'winger_fl_3200_fr_12s_ac',
'Winger FL 3200 FR 13S NAC': 'winger_fl_3200_fr_13s_nac',
'Winger FL 3488 HR 12S AC': 'winger_fl_3488_hr_12s_ac',
'Winger FL 3488 HR 12S': 'winger_fl_3488_hr_12s',
'Winger FL 35 HR PAS 15S': 'winger_fl_35_hr_pas_15s',
'Winger FL AMB 32SH 10+P AC': 'winger_fl_amb_32sh_10+p_ac',
'Winger FL AMB 32SH 10+P BS6 Phase 2': 'winger_fl_amb_32sh_10+p_bs6_phase_2',
'Winger FL AMB 35SH 10+P AC': 'winger_fl_amb_35sh_10+p_ac',
'Winger FL AMB 35SH 10+P BS6 Phase 2': 'winger_fl_amb_35sh_10+p_bs6_phase_2',
'Winger FL AMB-B 3200 7+P BS6 Phase 2': 'winger_fl_amb-b_3200_7+p_bs6_phase_2',
'Winger FL AMB-B 35 8+P BS6 Phase 2': 'winger_fl_amb-b_35_8+p_bs6_phase_2',
'Winger FL AMBB 3200 7+P AC': 'winger_fl_ambb_3200_7+p_ac',
'Winger RDE 9S 32WB PAS FR AC': 'winger_rde_9s_32wb_pas_fr_ac',
'Winger 21S 35WB Sch HR FPS': 'winger_21s_35wb_sch_hr_fps',
'Winger FL 35 HR PAS 15S SHL BS6 Phase 2': 'winger_fl_35_hr_pas_15s_shl_bs6_phase_2',
'Winger FL SK 3200 FR 13S AC': 'winger_fl_sk_3200_fr_13s_ac',
'Winger RDE 21S 35WB Sch HR BS6 Phase 2': 'winger_rde_21s_35wb_sch_hr_bs6_phase_2',
# --------- CITYRIDE SKL ---------
    'CITYRIDE SKL 19+B+D LP 410 30': 'cityride_skl_19+b+d_lp_410_30',
    'CITYRIDE SKL 19+C+D LP 410 31': 'cityride_skl_19+c+d_lp_410_31',
    'CITYRIDE SKL 27+A+D LP 410 33': 'cityride_skl_27+a+d_lp_410_33',
    'CITYRIDE SKL 27+A+D LP410 33': 'cityride_skl_27+a+d_lp410_33',
    'CITYRIDE SKL 29+A+D LP412 36': 'cityride_skl_29+a+d_lp412_36',
    'CITYRIDE SKL 35+A+D LP412 36': 'cityride_skl_35+a+d_lp412_36',
    'CITYRIDE SKL 37+A+D LP712 45': 'cityride_skl_37+a+d_lp712_45',
    'CITYRIDE SKL 41+A+D LP412 36': 'cityride_skl_41+a+d_lp412_36',
    'CITYRIDE SKL 41+A+D LP812 52': 'cityride_skl_41+a+d_lp812_52',
    'CITYRIDE SKL 45+A+D LP712 45': 'cityride_skl_45+a+d_lp712_45',
    'CITYRIDE SKL 50+A+D LP812 52': 'cityride_skl_50+a+d_lp812_52',
    'CITYRIDE SKL 53+A+D LP712 45': 'cityride_skl_53+a+d_lp712_45',
    'CITYRIDE SKL 59+A+D LP812 52': 'cityride_skl_59+a+d_lp812_52',

    # --------- LP / LPO ---------
    'LP 410 31 DIESEL': 'lp_410_31_diesel',
    'LP 410 33 DIESEL': 'lp_410_33_diesel',
    'LP 410 36 CNG': 'lp_410_36_cng',
    'LP 412 36 DIESEL': 'lp_412_36_diesel',
    'LP 712 39 DIESEL': 'lp_712_39_diesel',
    'LP 712 45 DIESEL': 'lp_712_45_diesel',
    'LP 716 45 DIESEL': 'lp_716_45_diesel',
    'LP 812 52 DIESEL': 'lp_812_52_diesel',
    'LP 910 52 CNG': 'lp_910_52_cng',
    'LP 913 52 CNG': 'lp_913_52_cng',
    'LP 916 52 DIESEL': 'lp_916_52_diesel',
    'LPO 11.6 52 CNG': 'lpo_11.6_52_cng',
    'LPO 11.6 54 DIESEL': 'lpo_11.6_54_diesel',
    'LPO 1316 54 DIESEL': 'lpo_1316_54_diesel',
    'LPO 1316 57 DIESEL': 'lpo_1316_57_diesel',
    'LPO 8.6 44 DIESEL': 'lpo_8.6_44_diesel',

    # --------- STARBUS ---------
    'STARBUS 19+B+D LP 410 30': 'starbus_19+b+d_lp_410_30',
    'STARBUS PRIME 34+A+D AC410 36G': 'starbus_prime_34+a+d_ac410_36g',
    'STARBUS PRIME 34+A+D LP410 36G': 'starbus_prime_34+a+d_lp410_36g',
    'STARBUS PRIME 34+A+D LP412 36': 'starbus_prime_34+a+d_lp412_36',
    'STARBUS PRIME 38+A+D LP410 36G': 'starbus_prime_38+a+d_lp410_36g',
    'STARBUS PRIME 38+A+D LP412 36': 'starbus_prime_38+a+d_lp412_36',
    'STARBUS PRIME 46+A+D AC 712 45': 'starbus_prime_46+a+d_ac_712_45',
    'STARBUS PRIME 46+A+D LP712 45': 'starbus_prime_46+a+d_lp712_45',
    'STARBUS PRIME 50+D AC LP916 52': 'starbus_prime_50+d_ac_lp916_52',
    'STARBUS PRIME 51+A+D 916 52': 'starbus_prime_51+a+d_916_52',
    'STARBUS PRIME 51+A+D AC 913 52G': 'starbus_prime_51+a+d_ac_913_52g',
    'STARBUS PRIME 51+A+D AC 916 52': 'starbus_prime_51+a+d_ac_916_52',
    'STARBUS PRIME 51+A+D LP812 52': 'starbus_prime_51+a+d_lp812_52',
    'STARBUS PRIME 51+A+D LP916 52': 'starbus_prime_51+a+d_lp916_52',
    'STARBUS PRIME 53+A+D LP712 45': 'starbus_prime_53+a+d_lp712_45',
    'STARBUS PRIME 59+A+D LP812 52': 'starbus_prime_59+a+d_lp812_52',
    'STARBUS SKL 19+A+D LP 410 29': 'starbus_skl_19+a+d_lp_410_29',

    # --------- ULTRA PRIME ---------
    'ULTRA PRIME 58+A+D LPO11.6 54': 'ultra_prime_58+a+d_lpo11.6_54',

}

def load_system_prompt():
    try:
        with open('system_prompt.txt', 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        return "You are a helpful car dealership assistant for Bhandari Automobiles. Help customers with car information, bookings, and dealership services."

def create_enhanced_system_prompt(base_prompt, knowledge_base_content):
    """Create enhanced system prompt with knowledge base content"""
    if knowledge_base_content:
        enhanced_prompt = f"""{base_prompt}

## KNOWLEDGE BASE
You have access to the following specific information about Bhandari Automobiles:

{knowledge_base_content}

**IMPORTANT**: When customers ask questions about details,addresses, contact information, hours, or other specific details, use the information from the Knowledge Base above to provide accurate answers. Don't just redirect to menus - answer their questions directly first, then offer additional help if appropriate.
"""
        return enhanced_prompt
    return base_prompt



def load_information_file():
    """Load information from text files for knowledge base"""
    try:
        # Load knowledge base
        knowledge_base = load_text_file("knowledge_base.txt")

        # You can also load information.txt if it exists
        # information = load_text_file("information.txt")

        # Combine both if needed
        combined_info = ""
        if knowledge_base:
            combined_info += f"Knowledge Base:\n{knowledge_base}\n\n"
        return combined_info.strip()
    except Exception as e:
        print(f"Error loading information files: {e}")
        return ""

